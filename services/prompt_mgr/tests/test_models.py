"""
Tests for the Prompt Manager models.
"""

import pytest
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from datetime import datetime

from services.prompt_mgr.models import Base, Prompt, Version, Experiment, ExperimentPrompt


@pytest.fixture
def engine():
    """Create an in-memory SQLite engine for testing."""
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    return engine


@pytest.fixture
def session(engine):
    """Create a new database session for testing."""
    Session = sessionmaker(bind=engine)
    session = Session()
    try:
        yield session
    finally:
        session.close()


def test_prompt_creation(session):
    """Test creating a prompt."""
    # Create a prompt
    prompt = Prompt(
        key="test_prompt",
        description="Test prompt description",
        tags=["test", "prompt"],
        is_active=True
    )
    
    session.add(prompt)
    session.commit()
    
    # Retrieve the prompt
    retrieved_prompt = session.query(Prompt).filter_by(key="test_prompt").first()
    
    # Check that the prompt was created correctly
    assert retrieved_prompt is not None
    assert retrieved_prompt.key == "test_prompt"
    assert retrieved_prompt.description == "Test prompt description"
    assert retrieved_prompt.tags == ["test", "prompt"]
    assert retrieved_prompt.is_active is True
    assert isinstance(retrieved_prompt.created_at, datetime)
    assert isinstance(retrieved_prompt.updated_at, datetime)


def test_version_creation(session):
    """Test creating a prompt version."""
    # Create a prompt
    prompt = Prompt(key="test_prompt", description="Test prompt")
    session.add(prompt)
    session.flush()
    
    # Create a version
    version = Version(
        prompt_id=prompt.id,
        version=1,
        content="This is a test prompt: {{variable}}",
        metadata={"model": "gpt-4", "temperature": 0.7},
        created_by="<EMAIL>"
    )
    
    session.add(version)
    session.commit()
    
    # Retrieve the version
    retrieved_version = session.query(Version).filter_by(prompt_id=prompt.id).first()
    
    # Check that the version was created correctly
    assert retrieved_version is not None
    assert retrieved_version.prompt_id == prompt.id
    assert retrieved_version.version == 1
    assert retrieved_version.content == "This is a test prompt: {{variable}}"
    assert retrieved_version.metadata == {"model": "gpt-4", "temperature": 0.7}
    assert retrieved_version.created_by == "<EMAIL>"
    assert isinstance(retrieved_version.created_at, datetime)


def test_experiment_creation(session):
    """Test creating an experiment."""
    # Create a prompt
    prompt = Prompt(key="test_prompt", description="Test prompt")
    session.add(prompt)
    session.flush()
    
    # Create versions
    version1 = Version(
        prompt_id=prompt.id,
        version=1,
        content="Version 1 content",
    )
    version2 = Version(
        prompt_id=prompt.id,
        version=2,
        content="Version 2 content",
    )
    
    session.add(version1)
    session.add(version2)
    session.flush()
    
    # Create an experiment
    experiment = Experiment(
        name="Test Experiment",
        description="Test experiment description",
        is_active=True,
        start_date=datetime.utcnow(),
        end_date=None,
        created_by="<EMAIL>"
    )
    
    session.add(experiment)
    session.flush()
    
    # Create experiment-prompt associations
    experiment_prompt1 = ExperimentPrompt(
        experiment_id=experiment.id,
        prompt_id=prompt.id,
        version_id=version1.id,
        traffic_allocation=50
    )
    
    experiment_prompt2 = ExperimentPrompt(
        experiment_id=experiment.id,
        prompt_id=prompt.id,
        version_id=version2.id,
        traffic_allocation=50
    )
    
    session.add(experiment_prompt1)
    session.add(experiment_prompt2)
    session.commit()
    
    # Retrieve the experiment
    retrieved_experiment = session.query(Experiment).filter_by(name="Test Experiment").first()
    
    # Check that the experiment was created correctly
    assert retrieved_experiment is not None
    assert retrieved_experiment.name == "Test Experiment"
    assert retrieved_experiment.description == "Test experiment description"
    assert retrieved_experiment.is_active is True
    assert isinstance(retrieved_experiment.start_date, datetime)
    assert retrieved_experiment.end_date is None
    assert retrieved_experiment.created_by == "<EMAIL>"
    
    # Check experiment-prompt associations
    experiment_prompts = session.query(ExperimentPrompt).filter_by(experiment_id=experiment.id).all()
    assert len(experiment_prompts) == 2
    
    # Check traffic allocation
    total_allocation = sum(ep.traffic_allocation for ep in experiment_prompts)
    assert total_allocation == 100


def test_prompt_validation(session):
    """Test prompt validation."""
    # Test that key cannot be empty
    with pytest.raises(ValueError):
        prompt = Prompt(key="", description="Test prompt")
        session.add(prompt)
        session.flush()
    
    with pytest.raises(ValueError):
        prompt = Prompt(key=None, description="Test prompt")
        session.add(prompt)
        session.flush()


def test_version_validation(session):
    """Test version validation."""
    # Create a prompt
    prompt = Prompt(key="test_prompt", description="Test prompt")
    session.add(prompt)
    session.flush()
    
    # Test that content cannot be empty
    with pytest.raises(ValueError):
        version = Version(
            prompt_id=prompt.id,
            version=1,
            content="",
        )
        session.add(version)
        session.flush()
    
    with pytest.raises(ValueError):
        version = Version(
            prompt_id=prompt.id,
            version=1,
            content=None,
        )
        session.add(version)
        session.flush()


def test_cascade_delete(session):
    """Test that deleting a prompt cascades to versions."""
    # Create a prompt
    prompt = Prompt(key="test_prompt", description="Test prompt")
    session.add(prompt)
    session.flush()
    
    # Create a version
    version = Version(
        prompt_id=prompt.id,
        version=1,
        content="Test content",
    )
    
    session.add(version)
    session.commit()
    
    # Delete the prompt
    session.delete(prompt)
    session.commit()
    
    # Check that the version was also deleted
    retrieved_version = session.query(Version).filter_by(prompt_id=prompt.id).first()
    assert retrieved_version is None
