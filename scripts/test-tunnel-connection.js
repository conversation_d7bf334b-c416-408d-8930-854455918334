#!/usr/bin/env node
/**
 * CopilotKit Tunnel Connection Test Script
 * 
 * This script tests the connection between your local FastAPI server and CopilotKit Cloud
 * through the tunnel. It verifies that requests are properly forwarded and that the
 * authentication is working correctly.
 */

require('dotenv').config();
const fetch = require('node-fetch');
const readline = require('readline');
const chalk = require('chalk');

// Create readline interface for user input
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Configuration
const FASTAPI_URL = process.env.FASTAPI_URL || 'http://localhost:8000';
const HEALTH_ENDPOINT = `${FASTAPI_URL}/health`;
const COPILOTKIT_ENDPOINT = `${FASTAPI_URL}/copilotkit`;

// Utility functions
async function checkLocalEndpoint(url, name) {
  try {
    console.log(chalk.blue(`Checking local ${name} endpoint (${url})...`));
    const response = await fetch(url);
    
    if (response.ok) {
      console.log(chalk.green(`✓ Local ${name} endpoint is accessible (${response.status})`));
      return { success: true, status: response.status };
    } else {
      console.log(chalk.red(`✗ Local ${name} endpoint returned status ${response.status}`));
      return { success: false, status: response.status };
    }
  } catch (error) {
    console.log(chalk.red(`✗ Error accessing local ${name} endpoint: ${error.message}`));
    return { success: false, error: error.message };
  }
}

async function checkTunnelEndpoint(url, name) {
  try {
    console.log(chalk.blue(`Checking tunnel ${name} endpoint (${url})...`));
    const response = await fetch(url);
    
    if (response.ok) {
      console.log(chalk.green(`✓ Tunnel ${name} endpoint is accessible (${response.status})`));
      return { success: true, status: response.status };
    } else {
      console.log(chalk.red(`✗ Tunnel ${name} endpoint returned status ${response.status}`));
      return { success: false, status: response.status };
    }
  } catch (error) {
    console.log(chalk.red(`✗ Error accessing tunnel ${name} endpoint: ${error.message}`));
    return { success: false, error: error.message };
  }
}

// Main test function
async function testTunnelConnection() {
  console.log(chalk.bold('\n=== CopilotKit Tunnel Connection Test ===\n'));
  
  // Check local FastAPI server
  console.log(chalk.bold('1. Checking local FastAPI server:'));
  const healthResult = await checkLocalEndpoint(HEALTH_ENDPOINT, 'Health');
  
  if (!healthResult.success) {
    console.log(chalk.red('\nLocal FastAPI server is not accessible.'));
    console.log(chalk.red('Please make sure your FastAPI server is running on port 8000.'));
    process.exit(1);
  }
  
  // Get tunnel URL from user
  console.log(chalk.bold('\n2. Tunnel Information:'));
  
  rl.question(chalk.blue('Please enter your tunnel URL (e.g., https://gray-frogs.tunnel.la): '), async (tunnelUrl) => {
    if (!tunnelUrl) {
      console.log(chalk.red('No tunnel URL provided. Exiting.'));
      rl.close();
      process.exit(1);
    }
    
    // Normalize tunnel URL (remove trailing slash)
    tunnelUrl = tunnelUrl.trim().replace(/\/$/, '');
    
    // Check tunnel health endpoint
    const tunnelHealthUrl = `${tunnelUrl}/health`;
    const tunnelHealthResult = await checkTunnelEndpoint(tunnelHealthUrl, 'Health');
    
    if (tunnelHealthResult.success) {
      // Check tunnel CopilotKit endpoint
      console.log(chalk.bold('\n3. Checking tunnel CopilotKit endpoint:'));
      
      // First check with OPTIONS request (CORS preflight)
      const tunnelCopilotKitUrl = `${tunnelUrl}/copilotkit`;
      
      try {
        console.log(chalk.blue(`Checking tunnel CopilotKit OPTIONS endpoint...`));
        const optionsResponse = await fetch(tunnelCopilotKitUrl, { 
          method: 'OPTIONS',
          headers: {
            'Origin': 'https://cloud.copilotkit.ai'
          }
        });
        
        if (optionsResponse.ok) {
          console.log(chalk.green(`✓ Tunnel CopilotKit OPTIONS endpoint is accessible (${optionsResponse.status})`));
        } else {
          console.log(chalk.red(`✗ Tunnel CopilotKit OPTIONS endpoint returned status ${optionsResponse.status}`));
        }
      } catch (error) {
        console.log(chalk.red(`✗ Error accessing tunnel CopilotKit OPTIONS endpoint: ${error.message}`));
      }
      
      // Check with authentication
      if (process.env.CPK_ENDPOINT_SECRET) {
        console.log(chalk.blue(`Checking tunnel CopilotKit endpoint with authentication...`));
        try {
          const response = await fetch(tunnelCopilotKitUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'X-CPK-Endpoint-Key': process.env.CPK_ENDPOINT_SECRET
            },
            body: JSON.stringify({
              agent: 'supervisor_agent',
              messages: [{ role: 'user', content: 'Hello' }],
              stream: false
            })
          });
          
          if (response.ok) {
            console.log(chalk.green(`✓ Tunnel CopilotKit endpoint authentication successful (${response.status})`));
            const data = await response.json();
            console.log(chalk.green(`✓ Received response from agent: ${JSON.stringify(data).substring(0, 100)}...`));
          } else {
            console.log(chalk.red(`✗ Tunnel CopilotKit endpoint authentication failed (${response.status})`));
            const text = await response.text();
            console.log(chalk.red(`  Response: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`));
          }
        } catch (error) {
          console.log(chalk.red(`✗ Error testing tunnel CopilotKit endpoint authentication: ${error.message}`));
        }
      } else {
        console.log(chalk.yellow('⚠ CPK_ENDPOINT_SECRET is not set. Skipping authentication test.'));
      }
      
      // Summary
      console.log(chalk.bold('\n=== Test Summary ===\n'));
      
      console.log(chalk.green('✓ Local FastAPI server is accessible'));
      
      if (tunnelHealthResult.success) {
        console.log(chalk.green('✓ Tunnel connection is working'));
        console.log(chalk.blue('\nYou can now configure a Remote Endpoint in CopilotKit Cloud with the following URL:'));
        console.log(chalk.bold(`${tunnelUrl}/copilotkit`));
      } else {
        console.log(chalk.red('✗ Tunnel connection is not working properly'));
        console.log(chalk.yellow('\nPlease check your tunnel configuration and try again.'));
      }
    } else {
      console.log(chalk.red('\nTunnel connection is not working.'));
      console.log(chalk.red('Please make sure your tunnel is running and the URL is correct.'));
    }
    
    console.log(chalk.blue('\nFor detailed instructions, see:'));
    console.log(chalk.blue('docs/migration/task-2.2.3-remote-endpoint-configuration.md'));
    console.log('\n');
    
    rl.close();
  });
}

// Run the test
testTunnelConnection().catch(error => {
  console.error(chalk.red(`Error during test: ${error.message}`));
  rl.close();
  process.exit(1);
});
