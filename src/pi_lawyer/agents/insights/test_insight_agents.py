"""
Tests for Insight Agents

This module contains tests for the Insight Agents implementation.
"""

import unittest
from unittest.mock import MagicMock

from src.pi_lawyer.agents.insights import document, daily, swarm


class TestInsightAgents(unittest.TestCase):
    """Tests for the Insight Agents."""

    def setUp(self):
        """Set up the test."""
        # Create a mock StateGraph
        self.mock_graph = MagicMock()
        
        # Mock the add_node method
        self.mock_graph.add_node = MagicMock()

    def test_document_register(self):
        """Test that the document agent register function adds nodes without error."""
        # Call the register function
        node_ids = document.register(self.mock_graph)
        
        # Verify that add_node was called for each node
        self.assertEqual(self.mock_graph.add_node.call_count, 2)
        
        # Verify that the correct node IDs were returned
        self.assertIn("collect_document_context", node_ids)
        self.assertIn("draft_document_insights", node_ids)
        
        # Verify that the nodes were added with the correct functions
        self.mock_graph.add_node.assert_any_call("collect_document_context", document.collect_context)
        self.mock_graph.add_node.assert_any_call("draft_document_insights", document.draft_doc)

    def test_daily_register(self):
        """Test that the daily agent register function adds nodes without error."""
        # Call the register function
        node_ids = daily.register(self.mock_graph)
        
        # Verify that add_node was called for each node
        self.assertEqual(self.mock_graph.add_node.call_count, 2)
        
        # Verify that the correct node IDs were returned
        self.assertIn("gather_daily_events", node_ids)
        self.assertIn("summarize_daily_activities", node_ids)
        
        # Verify that the nodes were added with the correct functions
        self.mock_graph.add_node.assert_any_call("gather_daily_events", daily.gather_events)
        self.mock_graph.add_node.assert_any_call("summarize_daily_activities", daily.summarize)

    def test_swarm_register(self):
        """Test that the swarm agent register function adds nodes without error."""
        # Call the register function
        node_ids = swarm.register(self.mock_graph)
        
        # Verify that add_node was called for each node
        self.assertEqual(self.mock_graph.add_node.call_count, 1)
        
        # Verify that the correct node IDs were returned
        self.assertIn("spawn_child_agents", node_ids)
        
        # Verify that the nodes were added with the correct functions
        self.mock_graph.add_node.assert_any_call("spawn_child_agents", swarm.spawn_children)


if __name__ == "__main__":
    unittest.main()
