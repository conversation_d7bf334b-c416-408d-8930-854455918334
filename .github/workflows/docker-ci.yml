name: Docker CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  build-and-test:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build backend
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.backend
          push: false
          load: true
          tags: pi-lawyer-backend:test
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      - name: Build frontend
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.frontend
          push: false
          load: true
          tags: pi-lawyer-frontend:test
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new

      - name: Run backend tests
        run: |
          docker run --rm pi-lawyer-backend:test pytest pi_lawyer/tests/

      - name: Run type checks
        run: |
          docker run --rm pi-lawyer-backend:test python -m mypy pi_lawyer/ --show-error-codes --ignore-missing-imports

      # Only run on main branch pushes
      - name: Login to Container Registry
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Build and push production images
        if: github.event_name == 'push' && github.ref == 'refs/heads/main'
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./Dockerfile.backend
          push: true
          tags: |
            ghcr.io/${{ github.repository }}/pi-lawyer-backend:latest
            ghcr.io/${{ github.repository }}/pi-lawyer-backend:${{ github.sha }}

      # Move the cache to avoid cache growing too large
      - name: Move Docker cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache
