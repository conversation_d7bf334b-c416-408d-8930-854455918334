#!/bin/bash
# CopilotKit Tunnel Setup Script
# This script sets up a tunnel between your local FastAPI server and CopilotKit Cloud

# Set default port
DEFAULT_PORT=8000
PORT=${1:-$DEFAULT_PORT}

# Check if npx is installed
if ! command -v npx &> /dev/null; then
    echo "Error: npx is not installed. Please install Node.js and npm."
    exit 1
fi

# Display banner
echo "========================================================"
echo "  CopilotKit Tunnel Setup"
echo "  Connecting local FastAPI server to CopilotKit Cloud"
echo "========================================================"
echo ""

# Check if the user is already logged in
echo "Checking CopilotKit login status..."
LOGIN_CHECK=$(npx copilotkit@latest whoami 2>&1)
if [[ $LOGIN_CHECK == *"You are not logged in"* ]]; then
    echo "You are not logged in to CopilotKit Cloud."
    echo "Please login first:"
    npx copilotkit@latest login
else
    echo "Already logged in as: $LOGIN_CHECK"
fi

echo ""
echo "Starting tunnel to port $PORT..."
echo "This will connect your local FastAPI server to CopilotKit Cloud."
echo "Press Ctrl+C to stop the tunnel."
echo ""

# Start the tunnel
npx copilotkit@latest dev --port $PORT

# This script will not reach here unless there's an error
echo "Tunnel stopped."
