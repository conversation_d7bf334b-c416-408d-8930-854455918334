"""
Base LLM Client

This module provides a base class for LLM clients with common functionality
for all LLM providers, including retry logic, logging, and error handling.
"""

import logging
import time
from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Union, Callable

# Set up logging
logger = logging.getLogger(__name__)


class LLMError(Exception):
    """Base exception for LLM-related errors."""
    pass


class RateLimitError(LLMError):
    """Exception raised when rate limits are hit."""
    pass


class AuthenticationError(LLMError):
    """Exception raised when authentication fails."""
    pass


class ServerError(LLMError):
    """Exception raised when the LLM server returns an error."""
    pass


class ClientError(LLMError):
    """Exception raised when there's a client-side error."""
    pass


class BaseLLMClient(ABC):
    """
    Base class for LLM clients.
    
    This class provides common functionality for all LLM clients, including
    retry logic, logging, and error handling.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        timeout: float = 60.0,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the LLM client.
        
        Args:
            api_key: The API key for the LLM provider
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            retry_backoff: Backoff factor for retry delay
            timeout: Request timeout in seconds
            logger: Logger instance
        """
        self.api_key = api_key
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.retry_backoff = retry_backoff
        self.timeout = timeout
        self.logger = logger or logging.getLogger(__name__)
    
    @abstractmethod
    async def generate(
        self,
        prompt: str,
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text from the LLM.
        
        Args:
            prompt: The prompt to send to the LLM
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional provider-specific parameters
            
        Returns:
            A dictionary containing the generated text and metadata
        """
        pass
    
    @abstractmethod
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a chat response from the LLM.
        
        Args:
            messages: The messages to send to the LLM
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional provider-specific parameters
            
        Returns:
            A dictionary containing the generated response and metadata
        """
        pass
    
    async def with_retries(
        self,
        func: Callable,
        *args,
        **kwargs
    ) -> Any:
        """
        Execute a function with retry logic.
        
        Args:
            func: The function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            The result of the function
            
        Raises:
            LLMError: If all retries fail
        """
        retries = 0
        delay = self.retry_delay
        
        while True:
            try:
                start_time = time.time()
                result = await func(*args, **kwargs)
                elapsed_time = time.time() - start_time
                
                self.logger.debug(
                    f"LLM request successful in {elapsed_time:.2f}s",
                    extra={"elapsed_time": elapsed_time}
                )
                
                return result
            
            except RateLimitError as e:
                retries += 1
                if retries > self.max_retries:
                    self.logger.error(
                        f"Rate limit exceeded after {retries} retries",
                        extra={"error": str(e), "retries": retries}
                    )
                    raise
                
                self.logger.warning(
                    f"Rate limit hit, retrying in {delay:.2f}s (retry {retries}/{self.max_retries})",
                    extra={"error": str(e), "retry": retries, "max_retries": self.max_retries}
                )
                
                await self._sleep(delay)
                delay *= self.retry_backoff
            
            except ServerError as e:
                retries += 1
                if retries > self.max_retries:
                    self.logger.error(
                        f"Server error after {retries} retries",
                        extra={"error": str(e), "retries": retries}
                    )
                    raise
                
                self.logger.warning(
                    f"Server error, retrying in {delay:.2f}s (retry {retries}/{self.max_retries})",
                    extra={"error": str(e), "retry": retries, "max_retries": self.max_retries}
                )
                
                await self._sleep(delay)
                delay *= self.retry_backoff
            
            except (AuthenticationError, ClientError) as e:
                # Don't retry authentication or client errors
                self.logger.error(
                    f"Authentication or client error: {str(e)}",
                    extra={"error": str(e)}
                )
                raise
            
            except Exception as e:
                self.logger.error(
                    f"Unexpected error: {str(e)}",
                    extra={"error": str(e)}
                )
                raise LLMError(f"Unexpected error: {str(e)}") from e
    
    async def _sleep(self, seconds: float) -> None:
        """
        Sleep for the specified number of seconds.
        
        This method can be overridden to use different sleep implementations
        based on the async framework being used.
        
        Args:
            seconds: The number of seconds to sleep
        """
        import asyncio
        await asyncio.sleep(seconds)
