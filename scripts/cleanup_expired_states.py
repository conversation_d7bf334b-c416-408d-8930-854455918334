#!/usr/bin/env python
"""
Clean up expired LangGraph states.

This script cleans up expired states for a tenant by deleting states that haven't been updated
in a specified number of days. It should be run periodically as a scheduled task.

This script respects the existing Row Level Security (RLS) policies by:
1. Using the Supabase service role to bypass RLS when needed
2. Explicitly filtering by tenant_id to maintain tenant isolation
3. Logging all operations for audit purposes

Usage:
    python scripts/cleanup_expired_states.py --tenant-id <tenant_id> [--max-age-days <days>] [--service-role]
"""

import os
import sys
import asyncio
import logging
import argparse
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.pi_lawyer.state.persistence_utils import cleanup_expired_states
from src.pi_lawyer.db.client import get_db_client, set_auth_token


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)


async def main():
    """Clean up expired states."""
    # Parse command line arguments
    parser = argparse.ArgumentParser(description="Clean up expired LangGraph states")
    parser.add_argument("--tenant-id", required=True, help="Tenant ID")
    parser.add_argument("--max-age-days", type=int, default=30, help="Maximum age of states in days")
    parser.add_argument("--service-role", action="store_true", help="Use service role to bypass RLS")
    parser.add_argument("--dry-run", action="store_true", help="Show what would be deleted without actually deleting")
    args = parser.parse_args()

    try:
        # If using service role, set the auth token
        if args.service_role:
            # Get the service role key from environment
            service_role_key = os.environ.get("SUPABASE_SERVICE_ROLE_KEY")
            if not service_role_key:
                logger.error("SUPABASE_SERVICE_ROLE_KEY environment variable not set")
                sys.exit(1)

            # Set the auth token to use the service role
            set_auth_token(service_role_key)
            logger.info("Using service role to bypass RLS")

        # Log the operation
        if args.dry_run:
            logger.info(f"DRY RUN: Simulating cleanup of expired states for tenant_id={args.tenant_id}...")
        else:
            logger.info(f"Cleaning up expired states for tenant_id={args.tenant_id}...")

        # Perform the cleanup
        deleted_count = await cleanup_expired_states(
            tenant_id=args.tenant_id,
            max_age_days=args.max_age_days,
            dry_run=args.dry_run
        )

        # Log the result
        if args.dry_run:
            logger.info(f"DRY RUN: Would have deleted {deleted_count} expired states")
        else:
            logger.info(f"Deleted {deleted_count} expired states")

    except Exception as e:
        logger.error(f"Failed to clean up expired states: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
