"""
Supervisor Router

This module provides a simple router function for the LangGraph StateGraph.
It inspects the state and returns the name of the next node to execute.

Usage:
    from pi_lawyer.agents.insights.supervisor.router import route_to_agent

    # Add the router to the StateGraph
    sg = StateGraph(AiLexState)
    sg.add_router(route_to_agent, {
        "supervisorAgent": "supervisorAgent",
        "intakeAgent": "intakeAgent",
        "researchAgent": "researchAgent",
        # ... other agents
    })
"""

import logging
from typing import Dict, Any, Optional

# Set up logging
logger = logging.getLogger(__name__)


def route_to_agent(state: Any) -> str:
    """
    Route to the appropriate agent based on the state.

    This function inspects the state and returns the name of the next node to execute.
    It is used as a router in the LangGraph StateGraph.

    Args:
        state: Current state or a string representing the next node

    Returns:
        The name of the next node to execute
    """
    # Handle the case where state is a string (already routed)
    if isinstance(state, str):
        logger.info(f"Already routed to: {state}")
        return state

    # Handle the case where state is a dictionary
    if isinstance(state, dict):
        # Check if there's a page_intent in the state (UI override)
        page_intent = state.get("page_intent")
        if page_intent:
            logger.info(f"Routing based on page_intent: {page_intent}")
            return page_intent

        # Check if there's a next node in the state (set by supervisor)
        next_node = state.get("next")
        if next_node:
            logger.info(f"Routing based on next: {next_node}")
            return next_node

    # Default to supervisor agent
    logger.info("No routing information found, defaulting to supervisorAgent")
    return "supervisorAgent"


def get_agent_args(state: Any) -> Dict[str, Any]:
    """
    Get the arguments for the agent from the state.

    Args:
        state: Current state or a string representing the next node

    Returns:
        The arguments for the agent
    """
    if isinstance(state, dict):
        return state.get("agent_args", {})
    return {}
