#!/usr/bin/env python
"""
Verify the LangGraph state persistence layer.

This script verifies that the state persistence layer is accessible and properly configured.
It should be run when the application is first deployed to ensure database connectivity.

Usage:
    python scripts/verify_state_persistence.py
"""

import os
import sys
import asyncio
import logging
from pathlib import Path

# Add the src directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.pi_lawyer.state.persistence_utils import verify_state_persistence


# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)


async def main():
    """Verify the state persistence layer."""
    try:
        logger.info("Verifying state persistence layer...")
        is_accessible = await verify_state_persistence()

        if is_accessible:
            logger.info("State persistence layer verification successful")
        else:
            logger.error("State persistence layer verification failed")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Failed to verify state persistence: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
