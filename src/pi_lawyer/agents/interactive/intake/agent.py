"""
Intake Agent Implementation

This module implements the Intake Agent, which is responsible for:
1. Collecting client information
2. Creating new cases
3. Checking for conflicts of interest
4. Supporting both client and staff intake flows

The Intake Agent serves as a dual-mode agent that can be used in both
client-facing and staff-facing contexts, with mode-specific behavior.

Usage:
    from pi_lawyer.agents.interactive.intake.agent import IntakeAgent, intake_client_router, intake_staff_router

    # Create an intake agent
    agent = IntakeAgent()

    # Execute the agent
    result = await agent.invoke(
        {"messages": [HumanMessage(content="I need to submit a new case")]},
        {"configurable": {"thread_id": "123", "tenant_id": "456"}}
    )
"""

import os
import json
import yaml
import logging
import importlib
from typing import Dict, Any, Optional, List, Literal, cast
from datetime import datetime, timezone
from pathlib import Path

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig

from pi_lawyer.agents.base_agent import BaseAgent
from pi_lawyer.agents.config import AgentConfig, get_agent_config
from pi_lawyer.shared.core.tools.executor import get_tool_executor

# Set up logging
logger = logging.getLogger(__name__)

# Define constants
DEFAULT_PRACTICE_AREA = "personal_injury"


class IntakeAgent(BaseAgent):
    """
    Intake Agent for collecting client information and creating new cases.

    This agent supports two modes:
    - client: For client-facing intake through the client portal
    - staff: For staff-facing intake through the admin interface

    The agent dynamically loads prompts and tool configurations based on the mode.
    """

    def __init__(self, config: Optional[AgentConfig] = None, agent_name: str = "intakeAgent", node_name: str = ""):
        """
        Initialize the intake agent.

        Args:
            config: Agent configuration
            agent_name: Name of the agent for LLM selection
            node_name: Name of the node for LLM selection
        """
        super().__init__(config, agent_name=agent_name, node_name=node_name)

    async def initialize(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Initialize the intake agent with mode-specific configuration.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Initializing intake agent")

        # Determine the mode (client or staff)
        mode = state.get("intake_mode", "client")
        state["intake_mode"] = mode

        # Load mode-specific configuration
        try:
            config_module = importlib.import_module(f"pi_lawyer.agents.interactive.intake.{mode}_flow.config")
            self.allowed_tools = getattr(config_module, "ALLOWED_TOOLS", [])
        except (ImportError, AttributeError) as e:
            logger.warning(f"Failed to load mode-specific configuration: {str(e)}")
            self.allowed_tools = []

        # Load mode-specific prompt
        try:
            prompt_path = Path(__file__).parent / f"{mode}_flow/prompt.md"
            with open(prompt_path, "r") as f:
                system_prompt = f.read()
        except FileNotFoundError:
            logger.warning(f"Prompt file not found for mode: {mode}")
            system_prompt = "I am an intake specialist for a law firm. I'll help collect information about your case."

        # Add system message if not present
        if "messages" in state and not any(m.get("type") == "system" for m in state["messages"]):
            state["messages"].append({
                "type": "system",
                "content": system_prompt
            })

        # Initialize state fields
        if "client" not in state:
            state["client"] = {}
        if "case" not in state:
            state["case"] = {}
        if "next" not in state:
            state["next"] = "initial_contact"

        # Initialize memory
        if "memory" in state:
            state["memory"]["initialized_at"] = datetime.now(timezone.utc).isoformat()
            state["memory"]["agent_name"] = self.name
            state["memory"]["agent_type"] = self.agent_type
            state["memory"]["agent_version"] = self.version
            state["memory"]["intake_mode"] = mode

        logger.info(f"Intake agent initialized in {mode} mode")
        return state

    async def execute(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Execute the intake agent with mode-specific behavior.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Executing intake agent")

        # Extract configurable values
        configurable = config.get("configurable", {})
        thread_id = configurable.get("thread_id", "unknown")
        tenant_id = configurable.get("tenant_id", "unknown")
        user_id = configurable.get("user_id", "unknown")

        # Get the mode
        mode = state.get("intake_mode", "client")

        # Load practice area template if specified
        practice_area = state.get("case", {}).get("practice_area", DEFAULT_PRACTICE_AREA)
        template = await self._load_practice_area_template(practice_area, tenant_id)

        # Prepare the prompt for the LLM
        prompt = self._prepare_llm_prompt(state, template, mode)

        # Call the LLM to determine the next step
        try:
            # Get the LLM client using the selector
            llm = await self._llm(state)

            # Call the LLM
            response = await llm.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.2,
                max_tokens=1000
            )

            # Extract the content from the response
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "")

            # Parse the response to determine the next step
            next_step = self._parse_llm_response(content)
            state["next"] = next_step

            # Add the LLM response to messages
            if "messages" in state:
                state["messages"].append({
                    "type": "ai",
                    "content": content
                })

            logger.info(f"Intake agent determined next step: {next_step}")

        except Exception as e:
            logger.error(f"Error calling LLM: {str(e)}")
            state["next"] = "FINISH"
            if "messages" in state:
                state["messages"].append({
                    "type": "ai",
                    "content": "I'm sorry, I encountered an error. Please try again later."
                })

        return state

    async def cleanup(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Clean up the intake agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Cleaning up intake agent")

        # Add to memory
        if "memory" in state:
            state["memory"]["cleaned_up_at"] = datetime.now(timezone.utc).isoformat()

        logger.info("Intake agent cleaned up")
        return state

    # Helper methods

    async def _load_practice_area_template(self, practice_area: str, tenant_id: str) -> Dict[str, Any]:
        """
        Load the practice area template.

        Args:
            practice_area: The practice area to load the template for
            tenant_id: The tenant ID

        Returns:
            The practice area template
        """
        try:
            template_path = Path(__file__).parent.parent.parent.parent / f"shared/intake_templates/{practice_area}.yaml"

            if template_path.exists():
                with open(template_path, "r") as f:
                    return yaml.safe_load(f)
            else:
                # Fall back to default template
                default_path = Path(__file__).parent.parent.parent.parent / "shared/intake_templates/default.yaml"
                if default_path.exists():
                    with open(default_path, "r") as f:
                        return yaml.safe_load(f)
                else:
                    logger.warning(f"No template found for practice area: {practice_area}")
                    return {"title": "Generic Intake", "fields": []}

        except Exception as e:
            logger.error(f"Error loading practice area template: {str(e)}")
            return {"title": "Generic Intake", "fields": []}

    def _prepare_llm_prompt(self, state: Dict[str, Any], template: Dict[str, Any], mode: str) -> str:
        """
        Prepare the prompt for the LLM.

        Args:
            state: Current state
            template: Practice area template
            mode: Intake mode (client or staff)

        Returns:
            The prompt for the LLM
        """
        # Get the last human message
        last_human_message = None
        if "messages" in state:
            for message in reversed(state["messages"]):
                if message.get("type") == "human":
                    last_human_message = message
                    break

        # Create a summary of the current state
        client = state.get("client", {})
        case = state.get("case", {})

        state_summary = "Current State:\n"
        state_summary += f"Mode: {mode}\n"
        state_summary += f"Client: {json.dumps(client, indent=2)}\n"
        state_summary += f"Case: {json.dumps(case, indent=2)}\n"

        # Add practice area template information
        state_summary += f"\nPractice Area: {template.get('title', 'Unknown')}\n"
        state_summary += "Required Fields:\n"
        for field in template.get("fields", []):
            field_id = field.get("id", "")
            field_prompt = field.get("prompt", "")
            field_value = None

            # Check if the field is already in the state
            if field_id in client:
                field_value = client[field_id]
            elif field_id in case:
                field_value = case[field_id]

            state_summary += f"- {field_id}: {field_prompt} (Current value: {field_value})\n"

        # Create the prompt
        prompt = f"""
        {state_summary}

        User message: {last_human_message.get('content', '') if last_human_message else ''}

        Based on the current state and user message, determine the next step in the intake process.
        Choose one of the following:
        - initial_contact: Welcome the user and ask for their name
        - collect_personal_info: Collect contact information
        - collect_case_details: Collect details about the case
        - check_conflicts: Check for conflicts of interest
        - summarize_and_confirm: Summarize collected information and confirm
        - save_client_info: Save client information to the database
        - FINISH: End the intake process

        Respond with your reasoning and the next step.
        """

        return prompt

    def _parse_llm_response(self, content: str) -> str:
        """
        Parse the LLM response to determine the next step.

        Args:
            content: The LLM response content

        Returns:
            The next step
        """
        # Default to FINISH if we can't determine the next step
        next_step = "FINISH"

        # Look for keywords in the response
        if "initial_contact" in content.lower():
            next_step = "initial_contact"
        elif "collect_personal_info" in content.lower():
            next_step = "collect_personal_info"
        elif "collect_case_details" in content.lower():
            next_step = "collect_case_details"
        elif "check_conflicts" in content.lower():
            next_step = "check_conflicts"
        elif "summarize_and_confirm" in content.lower():
            next_step = "summarize_and_confirm"
        elif "save_client_info" in content.lower():
            next_step = "save_client_info"

        return next_step
