#!/usr/bin/env python
"""
Run tests for the JWT middleware implementation.

This script runs the unit and integration tests for the JWT middleware.
"""

import os
import sys
import subprocess

def main():
    """Run the tests."""
    # Set environment variables for testing
    os.environ["SUPABASE_JWT_SECRET"] = "test-secret-for-jwt-middleware-testing"
    os.environ["APP_ENV"] = "test"

    # Run the tests
    test_files = [
        "tests/unit/middleware/test_jwt_middleware.py",
        "tests/integration/api/test_jwt_integration.py",
    ]

    print("Running JWT middleware tests...")
    result = subprocess.run(["pytest", "-v"] + test_files, env=os.environ)

    # Return the exit code
    return result.returncode

if __name__ == "__main__":
    sys.exit(main())
