"""
Base Tool

This module provides the base class for all tools.
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Generic, TypeVar, Optional, Type
from pydantic import BaseModel

# Set up logging
logger = logging.getLogger(__name__)

# Define type variables
T = TypeVar('T', bound=BaseModel)
U = TypeVar('U', bound=BaseModel)


class BaseTool(ABC, Generic[T, U]):
    """Base class for all tools."""
    
    name: str = ""
    description: str = ""
    input_schema: Type[T] = None
    output_schema: Type[U] = None
    
    @abstractmethod
    async def _execute(self, input_data: T) -> U:
        """
        Execute the tool.
        
        Args:
            input_data: Tool input
            
        Returns:
            Tool output
        """
        pass
    
    async def execute(self, **kwargs) -> U:
        """
        Execute the tool with keyword arguments.
        
        Args:
            **kwargs: Keyword arguments for the tool
            
        Returns:
            Tool output
        """
        logger.info(f"Executing tool: {self.name}")
        
        # Validate input
        if self.input_schema:
            input_data = self.input_schema(**kwargs)
        else:
            input_data = kwargs
        
        # Execute the tool
        result = await self._execute(input_data)
        
        # Validate output
        if self.output_schema and not isinstance(result, self.output_schema):
            result = self.output_schema(**result)
        
        return result
