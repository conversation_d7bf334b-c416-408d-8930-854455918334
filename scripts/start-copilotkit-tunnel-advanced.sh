#!/bin/bash
# Advanced CopilotKit Tunnel Setup Script
# This script sets up a tunnel between your local FastAPI server and CopilotKit Cloud
# using the configuration from config/copilotkit-tunnel.json

# Set script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"
CONFIG_FILE="$PROJECT_ROOT/config/copilotkit-tunnel.json"

# Check if jq is installed (for JSON parsing)
if ! command -v jq &> /dev/null; then
    echo "Warning: jq is not installed. Using default port 8000."
    PORT=8000
else
    # Get port from config file
    if [ -f "$CONFIG_FILE" ]; then
        PORT=$(jq -r '.port // 8000' "$CONFIG_FILE")
    else
        echo "Warning: Config file not found at $CONFIG_FILE. Using default port 8000."
        PORT=8000
    fi
fi

# Override port with command line argument if provided
if [ ! -z "$1" ]; then
    PORT=$1
fi

# Check if npx is installed
if ! command -v npx &> /dev/null; then
    echo "Error: npx is not installed. Please install Node.js and npm."
    exit 1
fi

# Check if FastAPI server is running on the specified port
if ! curl -s "http://localhost:$PORT/health" > /dev/null; then
    echo "Warning: FastAPI server does not appear to be running on port $PORT."
    echo "The tunnel will not work correctly unless the FastAPI server is running."
    
    read -p "Do you want to continue anyway? (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Exiting. Please start the FastAPI server first with:"
        echo "  python run_fastapi.py"
        echo "  or"
        echo "  uvicorn src.pi_lawyer.api.runtime:app --host 0.0.0.0 --port $PORT --reload"
        exit 1
    fi
fi

# Display banner
echo "========================================================"
echo "  Advanced CopilotKit Tunnel Setup"
echo "  Connecting local FastAPI server to CopilotKit Cloud"
echo "========================================================"
echo "  Port: $PORT"
echo "  Config: $CONFIG_FILE"
echo "========================================================"
echo ""

# Check if the user is already logged in
echo "Checking CopilotKit login status..."
LOGIN_CHECK=$(npx copilotkit@latest whoami 2>&1)
if [[ $LOGIN_CHECK == *"You are not logged in"* ]]; then
    echo "You are not logged in to CopilotKit Cloud."
    echo "Please login first:"
    npx copilotkit@latest login
else
    echo "Already logged in as: $LOGIN_CHECK"
fi

echo ""
echo "Starting tunnel to port $PORT..."
echo "This will connect your local FastAPI server to CopilotKit Cloud."
echo "Press Ctrl+C to stop the tunnel."
echo ""

# Start the tunnel
npx copilotkit@latest dev --port $PORT

# This script will not reach here unless there's an error
echo "Tunnel stopped."
