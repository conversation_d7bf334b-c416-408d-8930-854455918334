"""
Simple test script for the shared core components.

This script tests the shared core components without using pytest.
"""

import json
import sys
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Type

from pi_lawyer.shared.core.state import (
    AiLexState,
    StateModel,
    create_state,
    create_typed_state,
    add_messages
)


def test_create_state_model():
    """Test creating a state model."""
    state = StateModel(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent="intake"
    )
    
    assert state.tenant_id == "test_tenant"
    assert state.user_id == "test_user"
    assert state.thread_id == "test_thread"
    assert state.agent == "intake"
    assert state.locale == "en"
    assert state.messages == []
    assert state.memory == {}
    assert state.version == "1.0.0"
    
    print("✅ test_create_state_model passed")


def test_add_message():
    """Test adding a message to the state."""
    state = StateModel(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent="intake"
    )
    
    state.add_message("user", "Hello")
    
    assert len(state.messages) == 1
    assert state.messages[0]["role"] == "user"
    assert state.messages[0]["content"] == "Hello"
    assert "timestamp" in state.messages[0]
    
    print("✅ test_add_message passed")


def test_get_messages_as_langchain():
    """Test getting messages as LangChain message objects."""
    state = StateModel(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent="intake"
    )
    
    state.add_message("user", "Hello")
    state.add_message("assistant", "Hi there")
    state.add_message("system", "System message")
    
    messages = state.get_messages_as_langchain()
    
    assert len(messages) == 3
    assert messages[0].content == "Hello"
    assert messages[1].content == "Hi there"
    assert messages[2].content == "System message"
    
    print("✅ test_get_messages_as_langchain passed")


def test_set_get_memory():
    """Test setting and getting memory values."""
    state = StateModel(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent="intake"
    )
    
    state.set_memory("key1", "value1")
    state.set_memory("key2", {"nested": "value"})
    
    assert state.get_memory("key1") == "value1"
    assert state.get_memory("key2") == {"nested": "value"}
    assert state.get_memory("key3") is None
    assert state.get_memory("key3", "default") == "default"
    
    print("✅ test_set_get_memory passed")


def test_to_typed_dict():
    """Test converting to a TypedDict."""
    state = StateModel(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent="intake"
    )
    
    state.add_message("user", "Hello")
    state.set_memory("key1", "value1")
    
    typed_dict = state.to_typed_dict()
    
    assert typed_dict["tenant_id"] == "test_tenant"
    assert typed_dict["user_id"] == "test_user"
    assert typed_dict["thread_id"] == "test_thread"
    assert typed_dict["agent"] == "intake"
    assert typed_dict["locale"] == "en"
    assert len(typed_dict["messages"]) == 1
    assert typed_dict["messages"][0]["role"] == "user"
    assert typed_dict["memory"]["key1"] == "value1"
    
    print("✅ test_to_typed_dict passed")


def test_to_from_json():
    """Test converting to and from JSON."""
    state = StateModel(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent="intake"
    )
    
    state.add_message("user", "Hello")
    state.set_memory("key1", "value1")
    
    json_str = state.to_json()
    new_state = StateModel.from_json(json_str)
    
    assert new_state.tenant_id == state.tenant_id
    assert new_state.user_id == state.user_id
    assert new_state.thread_id == state.thread_id
    assert new_state.agent == state.agent
    assert new_state.locale == state.locale
    assert len(new_state.messages) == len(state.messages)
    assert new_state.messages[0]["role"] == state.messages[0]["role"]
    assert new_state.memory["key1"] == state.memory["key1"]
    
    print("✅ test_to_from_json passed")


def test_create_state():
    """Test creating a state."""
    state = create_state(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent_type="intake"
    )
    
    assert state["tenant_id"] == "test_tenant"
    assert state["user_id"] == "test_user"
    assert state["thread_id"] == "test_thread"
    assert state["agent"] == "intake"
    assert state["locale"] == "en"
    assert state["messages"] == []
    assert state["memory"] == {}
    
    print("✅ test_create_state passed")


def test_create_typed_state():
    """Test creating a typed state."""
    state = create_state(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent_type="intake"
    )
    
    typed_state = create_typed_state(state)
    
    assert typed_state["tenant_id"] == "test_tenant"
    assert typed_state["user_id"] == "test_user"
    assert typed_state["thread_id"] == "test_thread"
    assert typed_state["agent"] == "intake"
    
    print("✅ test_create_typed_state passed")


def test_add_messages():
    """Test adding messages to a state."""
    state = create_state(
        tenant_id="test_tenant",
        user_id="test_user",
        thread_id="test_thread",
        agent_type="intake"
    )
    
    messages = [
        {"role": "user", "content": "Hello"},
        {"role": "assistant", "content": "Hi there"}
    ]
    
    new_state = add_messages(state, messages)
    
    assert len(new_state["messages"]) == 2
    assert new_state["messages"][0]["role"] == "user"
    assert new_state["messages"][0]["content"] == "Hello"
    assert new_state["messages"][1]["role"] == "assistant"
    assert new_state["messages"][1]["content"] == "Hi there"
    
    print("✅ test_add_messages passed")


def run_all_tests():
    """Run all tests."""
    print("Running tests for shared core components...")
    
    test_create_state_model()
    test_add_message()
    test_get_messages_as_langchain()
    test_set_get_memory()
    test_to_typed_dict()
    test_to_from_json()
    test_create_state()
    test_create_typed_state()
    test_add_messages()
    
    print("\nAll tests passed! ✅")


if __name__ == "__main__":
    run_all_tests()
