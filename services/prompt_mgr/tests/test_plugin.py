"""
Tests for the Prompt Manager LangGraph plugin.
"""

import pytest
import httpx
import json
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime

from services.prompt_mgr.plugin import PromptNode, PromptNodeConfig


@pytest.fixture
def mock_response():
    """Create a mock HTTP response."""
    mock = MagicMock()
    mock.status_code = 200
    mock.json.return_value = {
        "id": 1,
        "key": "test_prompt",
        "description": "Test prompt description",
        "tags": ["test", "prompt"],
        "created_at": "2023-05-23T00:00:00Z",
        "updated_at": "2023-05-23T00:00:00Z",
        "is_active": True,
        "latest_version": {
            "id": 1,
            "version": 1,
            "content": "This is a test prompt: {{variable}}",
            "metadata": {"model": "gpt-4", "temperature": 0.7},
            "created_at": "2023-05-23T00:00:00Z",
            "created_by": "<EMAIL>"
        }
    }
    return mock


@pytest.fixture
def mock_http_client(mock_response):
    """Create a mock HTTP client."""
    client = AsyncMock()
    client.get.return_value = mock_response
    return client


@pytest.mark.asyncio
async def test_prompt_node_fetch(mock_http_client):
    """Test fetching a prompt with the PromptNode."""
    # Create a PromptNode with the mock client
    node = PromptNode(
        config=PromptNodeConfig(base_url="http://test"),
        http_client=mock_http_client
    )
    
    # Call the node
    result = await node(
        {
            "prompt_key": "test_prompt",
            "prompt_vars": {"variable": "test value"}
        },
        {}
    )
    
    # Check that the client was called correctly
    mock_http_client.get.assert_called_once_with("http://test/prompts/test_prompt")
    
    # Check the result
    assert "prompt" in result
    assert result["prompt"] == "This is a test prompt: test value"
    assert "prompt_metadata" in result
    assert result["prompt_metadata"]["key"] == "test_prompt"
    assert result["prompt_metadata"]["version"] == 1
    assert result["prompt_metadata"]["metadata"] == {"model": "gpt-4", "temperature": 0.7}


@pytest.mark.asyncio
async def test_prompt_node_fetch_with_experiment(mock_http_client):
    """Test fetching a prompt with an experiment ID."""
    # Create a PromptNode with the mock client
    node = PromptNode(
        config=PromptNodeConfig(base_url="http://test"),
        http_client=mock_http_client
    )
    
    # Call the node with an experiment ID
    result = await node(
        {
            "prompt_key": "test_prompt",
            "prompt_vars": {"variable": "test value"},
            "experiment_id": 123
        },
        {}
    )
    
    # Check that the client was called correctly
    mock_http_client.get.assert_called_once_with("http://test/prompts/test_prompt?experiment_id=123")
    
    # Check the result
    assert "prompt" in result
    assert result["prompt"] == "This is a test prompt: test value"


@pytest.mark.asyncio
async def test_prompt_node_error_handling(mock_http_client):
    """Test error handling in the PromptNode."""
    # Configure the mock client to raise an exception
    mock_http_client.get.side_effect = httpx.RequestError("Connection error")
    
    # Create a PromptNode with the mock client and fallback content
    node = PromptNode(
        config=PromptNodeConfig(
            base_url="http://test",
            fallback_content={"test_prompt": "Fallback prompt: {{variable}}"}
        ),
        http_client=mock_http_client
    )
    
    # Call the node
    result = await node(
        {
            "prompt_key": "test_prompt",
            "prompt_vars": {"variable": "test value"}
        },
        {}
    )
    
    # Check the result
    assert "error" in result
    assert "Connection error" in result["error"]
    assert result["prompt"] == "Fallback prompt: test value"
    assert result["prompt_metadata"]["source"] == "fallback"


@pytest.mark.asyncio
async def test_prompt_node_no_key():
    """Test calling the PromptNode without a prompt key."""
    # Create a PromptNode
    node = PromptNode(
        config=PromptNodeConfig(base_url="http://test"),
        http_client=AsyncMock()
    )
    
    # Call the node without a prompt key
    result = await node({}, {})
    
    # Check the result
    assert "error" in result
    assert "No prompt_key provided" in result["error"]
    assert result["prompt"] is None
    assert result["prompt_metadata"] is None


@pytest.mark.asyncio
async def test_prompt_node_variable_substitution(mock_http_client):
    """Test variable substitution in the PromptNode."""
    # Create a PromptNode with the mock client
    node = PromptNode(
        config=PromptNodeConfig(base_url="http://test"),
        http_client=mock_http_client
    )
    
    # Call the node with multiple variables
    result = await node(
        {
            "prompt_key": "test_prompt",
            "prompt_vars": {
                "variable": "test value",
                "another_var": 123,
                "bool_var": True
            }
        },
        {}
    )
    
    # Check the result
    assert result["prompt"] == "This is a test prompt: test value"
    
    # Update the mock response to include multiple variables
    mock_http_client.get.return_value.json.return_value["latest_version"]["content"] = (
        "Variables: {{variable}}, {{another_var}}, {{bool_var}}"
    )
    
    # Call the node again
    result = await node(
        {
            "prompt_key": "test_prompt",
            "prompt_vars": {
                "variable": "test value",
                "another_var": 123,
                "bool_var": True
            }
        },
        {}
    )
    
    # Check the result
    assert result["prompt"] == "Variables: test value, 123, True"


@pytest.mark.asyncio
async def test_prompt_node_close():
    """Test closing the PromptNode's HTTP client."""
    # Create a mock HTTP client
    mock_client = AsyncMock()
    
    # Create a PromptNode with the mock client
    node = PromptNode(
        config=PromptNodeConfig(base_url="http://test"),
        http_client=mock_client
    )
    
    # Close the node
    await node.close()
    
    # Check that the client was not closed (since it was provided externally)
    mock_client.aclose.assert_not_called()
    
    # Create a PromptNode without a client (it will create its own)
    with patch("httpx.AsyncClient") as mock_client_class:
        mock_client_instance = AsyncMock()
        mock_client_class.return_value = mock_client_instance
        
        node = PromptNode(config=PromptNodeConfig(base_url="http://test"))
        
        # Close the node
        await node.close()
        
        # Check that the client was closed
        mock_client_instance.aclose.assert_called_once()
