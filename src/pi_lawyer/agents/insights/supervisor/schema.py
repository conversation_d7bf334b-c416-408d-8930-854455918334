"""
Supervisor Agent Schema

This module defines the schema for the Supervisor Agent's structured output.
"""

from enum import Enum
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator


class AgentType(str, Enum):
    """Available agent types for routing."""
    INTAKE = "intakeAgent"
    RESEARCH = "researchAgent"
    TASK_CRUD = "taskCrudAgent"
    CALENDAR_CRUD = "calendarCrudAgent"
    DOCUMENT_DRAFT = "documentDraftAgent"
    INSIGHT_SWARM = "insightSwarmAgent"


class Classification(BaseModel):
    """
    Classification result from the Supervisor Agent.
    
    This model defines the structure of the classification result,
    including the agent to route to, arguments to pass, and confidence.
    """
    agent: AgentType = Field(..., description="The agent to route to")
    args: Dict[str, Any] = Field(default_factory=dict, description="Arguments to pass to the agent")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Confidence score (0.0-1.0)")
    
    @validator('confidence')
    def check_confidence(cls, v):
        """Ensure confidence is between 0 and 1."""
        if v < 0 or v > 1:
            return max(0.0, min(v, 1.0))
        return v


# Function calling schema for LLM
CLASSIFY_FN_SCHEMA = {
    "name": "classify_intent",
    "description": "Classify user intent and determine which agent should handle the request",
    "parameters": {
        "type": "object",
        "properties": {
            "agent": {
                "type": "string",
                "enum": [e.value for e in AgentType],
                "description": "The agent to route to"
            },
            "args": {
                "type": "object",
                "description": "Arguments to pass to the agent"
            },
            "confidence": {
                "type": "number",
                "minimum": 0,
                "maximum": 1,
                "description": "Confidence score (0.0-1.0)"
            }
        },
        "required": ["agent", "confidence"]
    }
}
