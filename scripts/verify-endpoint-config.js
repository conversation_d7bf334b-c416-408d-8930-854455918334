#!/usr/bin/env node
/**
 * CopilotKit Remote Endpoint Configuration Verification Script
 * 
 * This script verifies that the necessary environment variables for the CopilotKit
 * Remote Endpoint are set correctly and tests the connection to the FastAPI server.
 */

require('dotenv').config();
const fetch = require('node-fetch');
const chalk = require('chalk');

// Configuration
const REQUIRED_VARS = [
  { name: 'CPK_ENDPOINT_SECRET', mask: true },
  { name: 'CPK_ENDPOINT_ID', mask: false }
];

const FASTAPI_URL = process.env.FASTAPI_URL || 'http://localhost:8000';
const HEALTH_ENDPOINT = `${FASTAPI_URL}/health`;
const COPILOTKIT_ENDPOINT = `${FASTAPI_URL}/copilotkit`;

// Utility functions
function maskValue(value) {
  if (!value) return 'not set';
  if (value.length <= 8) return '********';
  return `${value.substring(0, 4)}...${value.substring(value.length - 4)}`;
}

async function checkEndpoint(url, name, options = {}) {
  try {
    console.log(chalk.blue(`Checking ${name} endpoint (${url})...`));
    const response = await fetch(url, options);
    
    if (response.ok) {
      console.log(chalk.green(`✓ ${name} endpoint is accessible (${response.status})`));
      return { success: true, status: response.status };
    } else {
      console.log(chalk.red(`✗ ${name} endpoint returned status ${response.status}`));
      return { success: false, status: response.status };
    }
  } catch (error) {
    console.log(chalk.red(`✗ Error accessing ${name} endpoint: ${error.message}`));
    return { success: false, error: error.message };
  }
}

// Main verification function
async function verifyConfiguration() {
  console.log(chalk.bold('\n=== CopilotKit Remote Endpoint Configuration Verification ===\n'));
  
  // Check environment variables
  console.log(chalk.bold('1. Checking environment variables:'));
  let allVarsSet = true;
  
  for (const { name, mask } of REQUIRED_VARS) {
    const value = process.env[name];
    if (value) {
      const displayValue = mask ? maskValue(value) : value;
      console.log(chalk.green(`✓ ${name} is set: ${displayValue}`));
    } else {
      console.log(chalk.red(`✗ ${name} is not set`));
      allVarsSet = false;
    }
  }
  
  if (!allVarsSet) {
    console.log(chalk.yellow('\nSome required environment variables are not set.'));
    console.log(chalk.yellow('Please update your .env file with the missing variables.'));
  } else {
    console.log(chalk.green('\nAll required environment variables are set.'));
  }
  
  // Check FastAPI server
  console.log(chalk.bold('\n2. Checking FastAPI server:'));
  const healthResult = await checkEndpoint(HEALTH_ENDPOINT, 'Health');
  
  if (healthResult.success) {
    // Check CopilotKit endpoint
    console.log(chalk.bold('\n3. Checking CopilotKit endpoint:'));
    
    // First check with OPTIONS request (CORS preflight)
    await checkEndpoint(COPILOTKIT_ENDPOINT, 'CopilotKit OPTIONS', { 
      method: 'OPTIONS',
      headers: {
        'Origin': 'https://cloud.copilotkit.ai'
      }
    });
    
    // Then check with a simple GET request
    const copilotKitResult = await checkEndpoint(COPILOTKIT_ENDPOINT, 'CopilotKit GET');
    
    // Finally, check with a POST request including the endpoint secret
    if (process.env.CPK_ENDPOINT_SECRET) {
      console.log(chalk.blue(`Checking CopilotKit endpoint with authentication...`));
      try {
        const response = await fetch(COPILOTKIT_ENDPOINT, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'X-CPK-Endpoint-Key': process.env.CPK_ENDPOINT_SECRET
          },
          body: JSON.stringify({
            agent: 'supervisor_agent',
            messages: [{ role: 'user', content: 'Hello' }],
            stream: false
          })
        });
        
        if (response.ok) {
          console.log(chalk.green(`✓ CopilotKit endpoint authentication successful (${response.status})`));
        } else {
          console.log(chalk.red(`✗ CopilotKit endpoint authentication failed (${response.status})`));
          const text = await response.text();
          console.log(chalk.red(`  Response: ${text.substring(0, 100)}${text.length > 100 ? '...' : ''}`));
        }
      } catch (error) {
        console.log(chalk.red(`✗ Error testing CopilotKit endpoint authentication: ${error.message}`));
      }
    }
  }
  
  // Summary
  console.log(chalk.bold('\n=== Verification Summary ===\n'));
  
  if (allVarsSet && healthResult.success) {
    console.log(chalk.green('✓ Basic configuration appears correct'));
    console.log(chalk.blue('\nNext steps:'));
    console.log(chalk.blue('1. Start the CopilotKit tunnel: npm run tunnel'));
    console.log(chalk.blue('2. Configure the Remote Endpoint in CopilotKit Cloud'));
    console.log(chalk.blue('3. Test the Remote Endpoint connection'));
  } else {
    console.log(chalk.yellow('⚠ Some configuration issues were detected'));
    console.log(chalk.blue('\nPlease fix the issues above before proceeding.'));
  }
  
  console.log(chalk.blue('\nFor detailed instructions, see:'));
  console.log(chalk.blue('docs/migration/task-2.2.3-remote-endpoint-configuration.md'));
  console.log('\n');
}

// Run the verification
verifyConfiguration().catch(error => {
  console.error(chalk.red(`Error during verification: ${error.message}`));
  process.exit(1);
});
