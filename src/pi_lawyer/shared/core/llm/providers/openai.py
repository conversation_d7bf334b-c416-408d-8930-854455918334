"""
OpenAI LLM Client

This module provides a client for the OpenAI API with retry logic, logging,
and error handling.
"""

import logging
import os
from typing import Any, Dict, List, Optional, Union

from openai import AsyncOpenAI, APIError, RateLimitError as OpenAIRateLimitError

from ..base import BaseLLMClient, RateLimitError, AuthenticationError, ServerError, ClientError

# Set up logging
logger = logging.getLogger(__name__)


class OpenAIClient(BaseLLMClient):
    """
    Client for the OpenAI API.
    
    This class provides methods for generating text and chat completions
    using the OpenAI API.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        api_base: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        timeout: float = 60.0,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the OpenAI client.
        
        Args:
            api_key: The API key for the OpenAI API
            api_base: The base URL for the OpenAI API
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            retry_backoff: Backoff factor for retry delay
            timeout: Request timeout in seconds
            logger: Logger instance
        """
        super().__init__(
            api_key=api_key,
            max_retries=max_retries,
            retry_delay=retry_delay,
            retry_backoff=retry_backoff,
            timeout=timeout,
            logger=logger
        )
        
        # Use environment variable if API key is not provided
        if self.api_key is None:
            self.api_key = os.environ.get("OPENAI_API_KEY")
            if self.api_key is None:
                raise ValueError("OpenAI API key not found")
        
        # Initialize the OpenAI client
        self.client = AsyncOpenAI(
            api_key=self.api_key,
            base_url=api_base,
            timeout=self.timeout
        )
    
    async def generate(
        self,
        prompt: str,
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text from the OpenAI API.
        
        Args:
            prompt: The prompt to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters for the API
            
        Returns:
            A dictionary containing the generated text and metadata
            
        Raises:
            LLMError: If the API request fails
        """
        # Convert the prompt to a message
        messages = [{"role": "user", "content": prompt}]
        
        # Generate a chat completion
        return await self.chat(
            messages=messages,
            model=model,
            temperature=temperature,
            max_tokens=max_tokens,
            stop_sequences=stop_sequences,
            **kwargs
        )
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a chat response from the OpenAI API.
        
        Args:
            messages: The messages to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters for the API
            
        Returns:
            A dictionary containing the generated response and metadata
            
        Raises:
            LLMError: If the API request fails
        """
        # Prepare the request parameters
        params = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            **kwargs
        }
        
        # Add optional parameters
        if max_tokens is not None:
            params["max_tokens"] = max_tokens
        
        if stop_sequences is not None:
            params["stop"] = stop_sequences
        
        # Make the API request with retries
        async def _make_request():
            try:
                response = await self.client.chat.completions.create(**params)
                
                # Extract the response text and metadata
                result = {
                    "text": response.choices[0].message.content,
                    "model": response.model,
                    "usage": {
                        "prompt_tokens": response.usage.prompt_tokens,
                        "completion_tokens": response.usage.completion_tokens,
                        "total_tokens": response.usage.total_tokens
                    },
                    "finish_reason": response.choices[0].finish_reason
                }
                
                return result
            
            except OpenAIRateLimitError as e:
                raise RateLimitError(str(e)) from e
            
            except APIError as e:
                if e.status_code == 401:
                    raise AuthenticationError(str(e)) from e
                elif e.status_code >= 500:
                    raise ServerError(str(e)) from e
                else:
                    raise ClientError(str(e)) from e
            
            except Exception as e:
                raise ClientError(str(e)) from e
        
        return await self.with_retries(_make_request)


# Singleton instance
_openai_client = None


def get_openai_client(
    api_key: Optional[str] = None,
    api_base: Optional[str] = None,
    force_new: bool = False
) -> OpenAIClient:
    """
    Get a singleton instance of the OpenAI client.
    
    Args:
        api_key: The API key for the OpenAI API
        api_base: The base URL for the OpenAI API
        force_new: Whether to force creation of a new client
        
    Returns:
        An OpenAI client
    """
    global _openai_client
    
    if _openai_client is None or force_new:
        _openai_client = OpenAIClient(
            api_key=api_key,
            api_base=api_base
        )
    
    return _openai_client
