"""
Telemetry Manager for collecting and reporting performance metrics.

This module provides a TelemetryManager class for collecting and reporting
performance metrics for the PI Lawyer AI application. It supports logging
metrics to the console, sending metrics to a monitoring service, and
collecting metrics for later analysis.
"""

import json
import logging
import time
import uuid
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

logger = logging.getLogger(__name__)


class TelemetryManager:
    """
    Telemetry Manager for collecting and reporting performance metrics.

    This class provides methods for collecting and reporting performance metrics
    for the PI Lawyer AI application. It supports logging metrics to the console,
    sending metrics to a monitoring service, and collecting metrics for later analysis.
    """

    def __init__(
        self,
        service_name: str,
        enable_logging: bool = True,
        enable_metrics: bool = True,
        log_level: int = logging.INFO,
    ):
        """
        Initialize the TelemetryManager.

        Args:
            service_name: The name of the service using this telemetry manager
            enable_logging: Whether to log metrics to the console
            enable_metrics: Whether to collect metrics for later analysis
            log_level: The log level to use for logging metrics
        """
        self.service_name = service_name
        self.enable_logging = enable_logging
        self.enable_metrics = enable_metrics
        self.log_level = log_level
        self.metrics: List[Dict[str, Any]] = []
        self.active_requests: Dict[str, Dict[str, Any]] = {}

    def start_request(
        self, request_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Start tracking a request.

        Args:
            request_id: A unique identifier for the request (generated if not provided)
            metadata: Additional metadata to include with the request

        Returns:
            The request ID
        """
        if request_id is None:
            request_id = str(uuid.uuid4())

        self.active_requests[request_id] = {
            "request_id": request_id,
            "service": self.service_name,
            "start_time": time.time(),
            "timestamp": datetime.now(datetime.timezone.utc).isoformat(),
            "metadata": metadata or {},
        }

        if self.enable_logging:
            logger.log(
                self.log_level,
                f"[{self.service_name}] Request started: {request_id}",
                extra={"telemetry": True, "request_id": request_id},
            )

        return request_id

    def update_request(self, request_id: str, data: Dict[str, Any]) -> None:
        """
        Update a request with additional data.

        Args:
            request_id: The request ID to update
            data: The data to add to the request
        """
        if request_id not in self.active_requests:
            logger.warning(
                f"[{self.service_name}] Attempted to update unknown request: {request_id}"
            )
            return

        self.active_requests[request_id].update(data)

        if self.enable_logging:
            logger.log(
                self.log_level,
                f"[{self.service_name}] Request updated: {request_id}",
                extra={"telemetry": True, "request_id": request_id, "data": data},
            )

    def mark_first_token(self, request_id: str) -> None:
        """
        Mark the time when the first token is received.

        Args:
            request_id: The request ID to update
        """
        if request_id not in self.active_requests:
            logger.warning(
                f"[{self.service_name}] Attempted to mark first token for unknown request: {request_id}"
            )
            return

        now = time.time()
        start_time = self.active_requests[request_id]["start_time"]
        first_token_latency = now - start_time

        self.active_requests[request_id].update({
            "first_token_time": now,
            "first_token_latency": first_token_latency,
        })

        if self.enable_logging:
            logger.log(
                self.log_level,
                f"[{self.service_name}] First token received for request {request_id} "
                f"in {first_token_latency:.3f}s",
                extra={
                    "telemetry": True,
                    "request_id": request_id,
                    "first_token_latency": first_token_latency,
                },
            )

    def complete_request(
        self, request_id: str, metrics: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Complete a request and record its metrics.

        Args:
            request_id: The request ID to complete
            metrics: Additional metrics to include with the request
        """
        if request_id not in self.active_requests:
            logger.warning(
                f"[{self.service_name}] Attempted to complete unknown request: {request_id}"
            )
            return

        now = time.time()
        request_data = self.active_requests[request_id]
        start_time = request_data["start_time"]
        duration = now - start_time

        # Update with completion metrics
        request_data.update({
            "end_time": now,
            "duration": duration,
            "completed": True,
            **(metrics or {}),
        })

        # Calculate first token latency if not already set
        if "first_token_latency" not in request_data and "first_token_time" in request_data:
            request_data["first_token_latency"] = request_data["first_token_time"] - start_time

        # Log completion
        if self.enable_logging:
            log_data = {
                "request_id": request_id,
                "duration": duration,
                "service": self.service_name,
            }
            if metrics:
                log_data.update(metrics)

            logger.log(
                self.log_level,
                f"[{self.service_name}] Request completed: {request_id} in {duration:.3f}s",
                extra={"telemetry": True, **log_data},
            )

        # Store metrics if enabled
        if self.enable_metrics:
            self.metrics.append(request_data)

        # Remove from active requests
        del self.active_requests[request_id]

    def get_metrics(self) -> List[Dict[str, Any]]:
        """
        Get all collected metrics.

        Returns:
            A list of all collected metrics
        """
        return self.metrics

    def clear_metrics(self) -> None:
        """Clear all collected metrics."""
        self.metrics = []

    def get_active_requests(self) -> Dict[str, Dict[str, Any]]:
        """
        Get all active requests.

        Returns:
            A dictionary of all active requests
        """
        return self.active_requests
