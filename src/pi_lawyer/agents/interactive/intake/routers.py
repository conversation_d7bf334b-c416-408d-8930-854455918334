"""
Intake Agent Routers

This module provides router nodes for the Intake Agent, which direct
requests to the appropriate intake flow based on the entry point.

Usage:
    from pi_lawyer.agents.interactive.intake.routers import intake_client_router, intake_staff_router

    # Add router nodes to the StateGraph
    sg.add_node("intakeClientRouter", intake_client_router)
    sg.add_node("intakeStaffRouter", intake_staff_router)
"""

import logging
from typing import Dict, Any
from langchain_core.runnables import RunnableConfig

# Set up logging
logger = logging.getLogger(__name__)


async def intake_client_router(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Router node for client intake.
    Sets the mode to "client" and directs to the IntakeAgent.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Routing to client intake")
    state["intake_mode"] = "client"
    state["next"] = "initial_contact"
    
    # Add a message indicating this is the client intake flow
    if "messages" in state and not any(m.get("content", "").startswith("Welcome to the client intake") for m in state["messages"]):
        state["messages"].append({
            "type": "system",
            "content": "Welcome to the client intake process. I'll guide you through submitting your case information."
        })
    
    return state


async def intake_staff_router(state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
    """
    Router node for staff intake.
    Sets the mode to "staff" and directs to the IntakeAgent.

    Args:
        state: Current state
        config: Runnable configuration

    Returns:
        Updated state
    """
    logger.info("Routing to staff intake")
    state["intake_mode"] = "staff"
    
    # Add a message indicating this is the staff intake flow
    if "messages" in state and not any(m.get("content", "").startswith("Welcome to the staff intake") for m in state["messages"]):
        state["messages"].append({
            "type": "system",
            "content": "Welcome to the staff intake process. I'll assist you in collecting client information."
        })
    
    # Staff can skip steps if data is pre-filled
    if state.get("client", {}).get("name") and state.get("case", {}).get("description"):
        logger.info("Pre-filled data detected, skipping to summarize_and_confirm")
        state["next"] = "summarize_and_confirm"
    else:
        state["next"] = "initial_contact"
        
    return state
