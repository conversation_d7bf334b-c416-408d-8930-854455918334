#!/bin/bash

# Simple Docker starter script for PI Lawyer AI on Apple Silicon
set -e

# Color codes
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Starting PI Lawyer AI development environment on Apple Silicon${NC}"

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo -e "${RED}❌ Error: Docker is not running${NC}"
    echo -e "${YELLOW}📋 Please start Docker Desktop and try again${NC}"
    exit 1
fi

# Check if we have .env file
if [ ! -f .env ]; then
    echo -e "${YELLOW}⚠️ No .env file found. Creating from .env.example${NC}"
    if [ -f .env.example ]; then
        cp .env.example .env
        echo -e "${YELLOW}🔄 Created .env from .env.example - please edit with your credentials${NC}"
    else
        echo -e "${RED}❌ No .env.example file found${NC}"
        exit 1
    fi
fi

# Create network if it doesn't exist
docker network create pi-lawyer-net 2>/dev/null || true

# Clean up any existing containers to avoid conflicts
echo -e "${BLUE}🧹 Cleaning up any existing containers...${NC}"
docker rm -f pi-lawyer-backend 2>/dev/null || true

# Start backend container
echo -e "${BLUE}🔄 Starting backend container...${NC}"
docker-compose up -d backend
echo -e "${GREEN}✅ Backend container started!${NC}"
echo
echo -e "${BLUE}📋 Backend logs will appear below. Press Ctrl+C to stop following (container will still run)${NC}"
docker-compose logs -f backend
