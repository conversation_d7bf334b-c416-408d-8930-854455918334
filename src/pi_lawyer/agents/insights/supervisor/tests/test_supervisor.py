"""
Tests for the Supervisor Agent

This module implements a comprehensive testing strategy for the Supervisor Agent:
1. Unit tests for intent classification
2. Integration tests for routing
3. Tests for async job queuing
4. Tests for fallback behavior
"""

import json
import pytest
import uuid
from unittest.mock import AsyncMock, patch, MagicMock
from typing import Dict, Any, List, Optional, Type, cast
from datetime import datetime, timezone

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langgraph.graph import StateGraph

from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent
from pi_lawyer.agents.insights.supervisor.router import route_to_agent, get_agent_args
from pi_lawyer.shared.core.tools.executor import get_tool_executor


# Helper function to create a test state
def make_state(msg: str, tenant: str = "tenant-123") -> Dict[str, Any]:
    """Create a test state with a user message."""
    return {
        "messages": [
            {"type": "human", "content": msg}
        ],
        "tenant_id": tenant,
        "user_id": "user-456",
        "thread_id": str(uuid.uuid4()),
        "memory": {}
    }


@pytest.mark.asyncio
class TestSupervisorAgent:
    """Tests for the Supervisor Agent."""

    @pytest.fixture
    def mock_llm_client(self):
        """Mock the LLM client."""
        # Create a mock LLM client
        mock_client = MagicMock()
        mock_client.chat_completion = AsyncMock()
        return mock_client

    @pytest.fixture
    def supervisor_agent(self, mock_llm_client):
        """Create a supervisor agent for testing."""
        return SupervisorAgent(llm_client=mock_llm_client)

    @pytest.fixture
    def mock_tool_executor(self):
        """Mock the tool executor."""
        with patch("pi_lawyer.agents.insights.supervisor.agent.get_tool_executor") as mock:
            # Configure the mock to return a valid response
            mock_instance = mock.return_value
            mock_instance.execute_tool = AsyncMock(return_value="job-123")
            yield mock_instance

    async def test_initialization(self, supervisor_agent):
        """Test agent initialization."""
        # The name is capitalized in the default config
        assert supervisor_agent.name == "SupervisorAgent"
        assert supervisor_agent.agent_type == "custom"
        # The description is generated by the default config
        assert "SupervisorAgent" in supervisor_agent.description
        assert supervisor_agent.version == "1.0.0"

    async def test_build_prompt(self, supervisor_agent):
        """Test building the prompt."""
        # Create a test state
        state = make_state("Research statute of limitations")

        # Build the prompt
        prompt = supervisor_agent._build_prompt(state)

        # Verify the prompt contains the user message
        assert "Research statute of limitations" in prompt
        assert "tenant-123" in prompt

    async def test_classify_intent(self, supervisor_agent, mock_llm_client):
        """Test intent classification."""
        # Configure the mock to return a valid response
        mock_llm_client.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": '{"agent": "researchAgent", "args": {"query": "statute of limitations"}}'
                    }
                }
            ]
        }

        # Create a test state
        state = make_state("Research statute of limitations")

        # Classify intent
        result = await supervisor_agent._classify_intent(state)

        # Verify the result
        assert result["agent"] == "researchAgent"
        assert "query" in result["args"]
        assert result["args"]["query"] == "statute of limitations"
        assert result["confidence"] == 1.0

    async def test_classify_intent_invalid_json(self, supervisor_agent, mock_llm_client):
        """Test intent classification with invalid JSON response."""
        # Configure the mock to return an invalid response
        mock_llm_client.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": "This is not valid JSON"
                    }
                }
            ]
        }

        # Create a test state
        state = make_state("Research statute of limitations")

        # Classify intent
        result = await supervisor_agent._classify_intent(state)

        # Verify the result uses the default agent
        assert result["agent"] == "intakeAgent"
        assert result["confidence"] == 0.0

    async def test_classify_intent_llm_error(self, supervisor_agent, mock_llm_client):
        """Test intent classification with LLM error."""
        # Configure the mock to raise an exception
        mock_llm_client.chat_completion.side_effect = Exception("LLM error")

        # Create a test state
        state = make_state("Research statute of limitations")

        # Classify intent
        result = await supervisor_agent._classify_intent(state)

        # Verify the result uses the default agent
        assert result["agent"] == "intakeAgent"
        assert result["confidence"] == 0.0

    async def test_routing_to_interactive(self, supervisor_agent, mock_llm_client):
        """Test routing to an interactive agent."""
        # Configure the mock to return a valid response
        mock_llm_client.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": '{"agent": "taskCrudAgent", "args": {"title": "New task", "due_date": "2025-05-23"}}'
                    }
                }
            ]
        }

        # Create a test state
        state = make_state("Add a task for next Friday")

        # Execute the agent
        result = await supervisor_agent.execute(state, {})

        # Verify the result
        assert result["next"] == "taskCrudAgent"
        assert "agent_args" in result
        assert result["agent_args"]["title"] == "New task"
        assert result["agent_args"]["due_date"] == "2025-05-23"

    async def test_async_queue(self, supervisor_agent, mock_llm_client, mock_tool_executor):
        """Test queuing an async job."""
        # Configure the mock to return a valid response
        mock_llm_client.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": '{"agent": "documentDraftAgent", "args": {"template": "settlement_letter", "matter_id": "123"}}'
                    }
                }
            ]
        }

        # Create a test state
        state = make_state("Draft a settlement letter")

        # Execute the agent
        result = await supervisor_agent.execute(state, {})

        # Verify the result
        assert result["next"] == "documentDraftAgent"
        assert "async_job_id" in result
        assert result["async_job_id"] == "job-123"

        # Verify the tool was called with the correct arguments
        mock_tool_executor.execute_tool.assert_called_once()
        args = mock_tool_executor.execute_tool.call_args[1]
        assert args["tool_name"] == "enqueue_async_job"
        assert args["tool_args"]["tool_name"] == "documentDraftAgent"
        assert args["tool_args"]["params"]["template"] == "settlement_letter"
        assert args["tool_args"]["params"]["matter_id"] == "123"
        assert args["tool_args"]["tenant_id"] == "tenant-123"

    async def test_fallback(self, supervisor_agent, mock_llm_client):
        """Test fallback behavior."""
        # Configure the mock to return a response with low confidence
        mock_llm_client.chat_completion.return_value = {
            "choices": [
                {
                    "message": {
                        "content": '{"agent": "unknownAgent", "args": {}}'
                    }
                }
            ]
        }

        # Create a test state
        state = make_state("🦄🦄🦄")

        # Execute the agent
        result = await supervisor_agent.execute(state, {})

        # Verify the result uses the default agent
        assert result["next"] == "intakeAgent"
        assert "fallback_reason" in result["memory"]

        # Verify a message was added
        assert any("I'm not sure I understood" in m["content"] for m in result["messages"] if m["type"] == "ai")

    async def test_router_function(self):
        """Test the router function."""
        # Test with page_intent
        state = {"page_intent": "intakeAgent"}
        assert route_to_agent(state) == "intakeAgent"

        # Test with next
        state = {"next": "researchAgent"}
        assert route_to_agent(state) == "researchAgent"

        # Test with no routing information
        state = {}
        assert route_to_agent(state) == "supervisorAgent"

    async def test_get_agent_args(self):
        """Test getting agent arguments."""
        # Test with agent_args
        state = {"agent_args": {"query": "test"}}
        assert get_agent_args(state) == {"query": "test"}

        # Test with no agent_args
        state = {}
        assert get_agent_args(state) == {}
