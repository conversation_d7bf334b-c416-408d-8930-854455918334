"""
Gemini LLM Client

This module provides a client for the Google Gemini API with retry logic, logging,
and error handling.
"""

import logging
import os
from typing import Any, Dict, List, Optional, Union

import google.generativeai as genai
from google.generativeai.types import GenerationConfig
from google.api_core.exceptions import ResourceExhausted, Unauthenticated, InternalServerError

from ..base import BaseLLMClient, RateLimitError, AuthenticationError, ServerError, ClientError

# Set up logging
logger = logging.getLogger(__name__)


class GeminiClient(BaseLLMClient):
    """
    Client for the Google Gemini API.
    
    This class provides methods for generating text and chat completions
    using the Google Gemini API.
    """
    
    def __init__(
        self,
        api_key: Optional[str] = None,
        max_retries: int = 3,
        retry_delay: float = 1.0,
        retry_backoff: float = 2.0,
        timeout: float = 60.0,
        logger: Optional[logging.Logger] = None
    ):
        """
        Initialize the Gemini client.
        
        Args:
            api_key: The API key for the Gemini API
            max_retries: Maximum number of retries for failed requests
            retry_delay: Initial delay between retries in seconds
            retry_backoff: Backoff factor for retry delay
            timeout: Request timeout in seconds
            logger: Logger instance
        """
        super().__init__(
            api_key=api_key,
            max_retries=max_retries,
            retry_delay=retry_delay,
            retry_backoff=retry_backoff,
            timeout=timeout,
            logger=logger
        )
        
        # Use environment variable if API key is not provided
        if self.api_key is None:
            self.api_key = os.environ.get("GEMINI_API_KEY")
            if self.api_key is None:
                raise ValueError("Gemini API key not found")
        
        # Initialize the Gemini client
        genai.configure(api_key=self.api_key)
    
    async def generate(
        self,
        prompt: str,
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate text from the Gemini API.
        
        Args:
            prompt: The prompt to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters for the API
            
        Returns:
            A dictionary containing the generated text and metadata
            
        Raises:
            LLMError: If the API request fails
        """
        # Create a generation config
        generation_config = GenerationConfig(
            temperature=temperature,
            top_p=kwargs.get("top_p", 1.0),
            top_k=kwargs.get("top_k", 1),
            candidate_count=1
        )
        
        # Add optional parameters
        if max_tokens is not None:
            generation_config.max_output_tokens = max_tokens
        
        if stop_sequences is not None:
            generation_config.stop_sequences = stop_sequences
        
        # Make the API request with retries
        async def _make_request():
            try:
                # Initialize the model
                gemini_model = genai.GenerativeModel(model)
                
                # Generate content
                response = await gemini_model.generate_content_async(
                    prompt,
                    generation_config=generation_config
                )
                
                # Extract the response text and metadata
                result = {
                    "text": response.text,
                    "model": model,
                    "usage": {}  # Gemini doesn't provide token usage info
                }
                
                return result
            
            except ResourceExhausted as e:
                raise RateLimitError(str(e)) from e
            
            except Unauthenticated as e:
                raise AuthenticationError(str(e)) from e
            
            except InternalServerError as e:
                raise ServerError(str(e)) from e
            
            except Exception as e:
                raise ClientError(str(e)) from e
        
        return await self.with_retries(_make_request)
    
    async def chat(
        self,
        messages: List[Dict[str, str]],
        model: str,
        temperature: float = 0.7,
        max_tokens: Optional[int] = None,
        stop_sequences: Optional[List[str]] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Generate a chat response from the Gemini API.
        
        Args:
            messages: The messages to send to the API
            model: The model to use
            temperature: The temperature to use for generation
            max_tokens: The maximum number of tokens to generate
            stop_sequences: Sequences that stop generation
            **kwargs: Additional parameters for the API
            
        Returns:
            A dictionary containing the generated response and metadata
            
        Raises:
            LLMError: If the API request fails
        """
        # Create a generation config
        generation_config = GenerationConfig(
            temperature=temperature,
            top_p=kwargs.get("top_p", 1.0),
            top_k=kwargs.get("top_k", 1),
            candidate_count=1
        )
        
        # Add optional parameters
        if max_tokens is not None:
            generation_config.max_output_tokens = max_tokens
        
        if stop_sequences is not None:
            generation_config.stop_sequences = stop_sequences
        
        # Make the API request with retries
        async def _make_request():
            try:
                # Initialize the model
                gemini_model = genai.GenerativeModel(model)
                
                # Convert messages to Gemini format
                gemini_messages = []
                
                for message in messages:
                    role = message["role"]
                    content = message["content"]
                    
                    # Map roles to Gemini roles
                    if role == "system":
                        # Add system message as a user message at the beginning
                        gemini_messages.append({"role": "user", "parts": [content]})
                        # Add a placeholder assistant response
                        gemini_messages.append({"role": "model", "parts": ["I'll help you with that."]})
                    elif role == "user":
                        gemini_messages.append({"role": "user", "parts": [content]})
                    elif role == "assistant":
                        gemini_messages.append({"role": "model", "parts": [content]})
                
                # Create a chat session
                chat = gemini_model.start_chat(history=gemini_messages[:-1])
                
                # Generate response
                response = await chat.send_message_async(
                    gemini_messages[-1]["parts"][0],
                    generation_config=generation_config
                )
                
                # Extract the response text and metadata
                result = {
                    "text": response.text,
                    "model": model,
                    "usage": {}  # Gemini doesn't provide token usage info
                }
                
                return result
            
            except ResourceExhausted as e:
                raise RateLimitError(str(e)) from e
            
            except Unauthenticated as e:
                raise AuthenticationError(str(e)) from e
            
            except InternalServerError as e:
                raise ServerError(str(e)) from e
            
            except Exception as e:
                raise ClientError(str(e)) from e
        
        return await self.with_retries(_make_request)


# Singleton instance
_gemini_client = None


def get_gemini_client(
    api_key: Optional[str] = None,
    force_new: bool = False
) -> GeminiClient:
    """
    Get a singleton instance of the Gemini client.
    
    Args:
        api_key: The API key for the Gemini API
        force_new: Whether to force creation of a new client
        
    Returns:
        A Gemini client
    """
    global _gemini_client
    
    if _gemini_client is None or force_new:
        _gemini_client = GeminiClient(
            api_key=api_key
        )
    
    return _gemini_client
