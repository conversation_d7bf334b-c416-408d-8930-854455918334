name: Authentication Tests

on:
  # Run on every pull request
  pull_request:
    branches: [ main, develop ]

  # Allow manual triggering
  workflow_dispatch:

jobs:
  auth-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 60

    # Use Node.js environment
    steps:
    - uses: actions/checkout@v4

    - name: Use Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Install Dependencies
      run: npm install

    - name: Run JWT Claims Tests
      run: npm run test:jwt
      env:
        # Securely pass Supabase credentials
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_KEY: ${{ secrets.SUPABASE_SERVICE_KEY }}

    - name: Run Authentication Route Tests
      run: npm run test:auth
      env:
        NEXT_PUBLIC_SUPABASE_URL: ${{ secrets.SUPABASE_URL }}
        SUPABASE_SERVICE_KEY: ${{ secrets.SUPABASE_SERVICE_KEY }}

    # Optional: Send notification on failure
    - name: Slack Notification
      if: failure()
      uses: 8398a7/action-slack@v3
      with:
        status: ${{ job.status }}
        text: Authentication tests failed in the pipeline
        webhook_url: ${{ secrets.SLACK_WEBHOOK }}
