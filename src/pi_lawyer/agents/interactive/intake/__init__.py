"""
Intake Agent Package

This package provides the Intake Agent implementation, which is responsible for
collecting client information, creating new cases, and checking for conflicts of interest.

The Intake Agent supports two modes:
1. Client mode: For client-facing intake through the client portal
2. Staff mode: For staff-facing intake through the admin interface

Usage:
    from pi_lawyer.agents.interactive.intake import IntakeAgent, intake_client_router, intake_staff_router

    # Create an intake agent
    agent = IntakeAgent()

    # Execute the agent
    result = await agent.invoke(
        {"messages": [HumanMessage(content="I need to submit a new case")]},
        {"configurable": {"thread_id": "123", "tenant_id": "456"}}
    )
"""

from pi_lawyer.agents.interactive.intake.agent import IntakeAgent
from pi_lawyer.agents.interactive.intake.routers import intake_client_router, intake_staff_router
from pi_lawyer.agents.interactive.intake.tasks import (
    initial_contact,
    collect_personal_info,
    collect_case_details,
    check_conflicts,
    summarize_and_confirm,
    save_client_info
)

__all__ = [
    "IntakeAgent",
    "intake_client_router",
    "intake_staff_router",
    "initial_contact",
    "collect_personal_info",
    "collect_case_details",
    "check_conflicts",
    "summarize_and_confirm",
    "save_client_info"
]
