"""
LLM Configuration Models

This module provides configuration models for LLM providers, including
model selection, API key management, and provider-specific settings.
"""

import os
from enum import Enum
from typing import Dict, List, Optional, Union, Literal

from pydantic import BaseModel, Field, model_validator


class LLMProvider(str, Enum):
    """Supported LLM providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GEMINI = "gemini"
    GROQ = "groq"
    VOYAGE = "voyage"
    MOCK = "mock"


class LLMConfig(BaseModel):
    """
    Configuration for an LLM provider.

    Attributes:
        provider: The LLM provider
        model: The model to use
        api_key: The API key for the provider
        api_key_env_var: Environment variable name for the API key
        api_base_url: Base URL for the API
        temperature: The temperature to use for generation
        max_tokens: The maximum number of tokens to generate
        top_p: The top-p value to use for generation
        frequency_penalty: The frequency penalty to use for generation
        presence_penalty: The presence penalty to use for generation
        stop_sequences: Sequences that stop generation
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries for failed requests
        retry_delay: Initial delay between retries in seconds
        retry_backoff: Backoff factor for retry delay
    """
    provider: Union[LLMProvider, str] = LLMProvider.OPENAI
    model: str = "gpt-4o"
    api_key: Optional[str] = None
    api_key_env_var: Optional[str] = None
    api_base_url: Optional[str] = None
    temperature: float = 0.7
    max_tokens: Optional[int] = 1000
    top_p: float = 1.0
    frequency_penalty: float = 0.0
    presence_penalty: float = 0.0
    stop_sequences: Optional[List[str]] = None
    timeout: float = 60.0
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff: float = 2.0

    @model_validator(mode='after')
    def set_api_key_from_env(self) -> 'LLMConfig':
        """Set the API key from the environment if not provided."""
        if self.api_key is None:
            # If api_key_env_var is provided, use that
            if self.api_key_env_var:
                self.api_key = os.environ.get(self.api_key_env_var)
            else:
                # Otherwise, use the default environment variable for the provider
                provider_env_vars = {
                    LLMProvider.OPENAI: "OPENAI_API_KEY",
                    LLMProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
                    LLMProvider.GEMINI: "GEMINI_API_KEY",
                    LLMProvider.GROQ: "GROQ_API_KEY",
                    LLMProvider.VOYAGE: "VOYAGE_API_KEY",
                }
                
                provider = self.provider
                if isinstance(provider, str):
                    provider = LLMProvider(provider)
                
                env_var = provider_env_vars.get(provider)
                if env_var:
                    self.api_key = os.environ.get(env_var)
        
        return self


class LLMMessage(BaseModel):
    """
    A message for an LLM conversation.

    Attributes:
        role: The role of the message sender (system, user, assistant)
        content: The content of the message
    """
    role: Literal["system", "user", "assistant"] = "user"
    content: str


class LLMResponse(BaseModel):
    """
    A response from an LLM.

    Attributes:
        content: The content of the response
        model: The model that generated the response
        usage: Usage information for the request
        finish_reason: The reason the generation finished
    """
    content: str
    model: str
    usage: Dict[str, int] = Field(default_factory=dict)
    finish_reason: Optional[str] = None


class LLMUsage(BaseModel):
    """
    Usage information for an LLM request.

    Attributes:
        prompt_tokens: The number of tokens in the prompt
        completion_tokens: The number of tokens in the completion
        total_tokens: The total number of tokens used
    """
    prompt_tokens: int = 0
    completion_tokens: int = 0
    total_tokens: int = 0


class EmbeddingConfig(BaseModel):
    """
    Configuration for embedding models.

    Attributes:
        provider: The embedding provider
        model: The model to use
        api_key: The API key for the provider
        api_key_env_var: Environment variable name for the API key
        api_base_url: Base URL for the API
        dimensions: The dimensions of the embeddings
        timeout: Request timeout in seconds
        max_retries: Maximum number of retries for failed requests
    """
    provider: Union[LLMProvider, str] = LLMProvider.OPENAI
    model: str = "text-embedding-3-large"
    api_key: Optional[str] = None
    api_key_env_var: Optional[str] = None
    api_base_url: Optional[str] = None
    dimensions: Optional[int] = None
    timeout: float = 60.0
    max_retries: int = 3
    retry_delay: float = 1.0
    retry_backoff: float = 2.0

    @model_validator(mode='after')
    def set_api_key_from_env(self) -> 'EmbeddingConfig':
        """Set the API key from the environment if not provided."""
        if self.api_key is None:
            # If api_key_env_var is provided, use that
            if self.api_key_env_var:
                self.api_key = os.environ.get(self.api_key_env_var)
            else:
                # Otherwise, use the default environment variable for the provider
                provider_env_vars = {
                    LLMProvider.OPENAI: "OPENAI_API_KEY",
                    LLMProvider.ANTHROPIC: "ANTHROPIC_API_KEY",
                    LLMProvider.GEMINI: "GEMINI_API_KEY",
                    LLMProvider.GROQ: "GROQ_API_KEY",
                    LLMProvider.VOYAGE: "VOYAGE_API_KEY",
                }
                
                provider = self.provider
                if isinstance(provider, str):
                    provider = LLMProvider(provider)
                
                env_var = provider_env_vars.get(provider)
                if env_var:
                    self.api_key = os.environ.get(env_var)
        
        return self
