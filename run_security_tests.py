#!/usr/bin/env python
"""
Run tests for the security middleware implementation.

This script runs the unit tests for the security middleware components:
- CORS middleware
- Security headers middleware
- Rate limiting middleware
"""

import os
import sys
import subprocess

def main():
    """Run the tests."""
    # Set environment variables for testing
    os.environ["ENABLE_CSP"] = "true"
    os.environ["ENABLE_RATE_LIMIT"] = "true"
    os.environ["RATE_LIMIT"] = "10"
    os.environ["RATE_LIMIT_WINDOW"] = "60"
    os.environ["CORS_ORIGINS"] = "http://localhost:3000,https://example.com"
    
    # Run the tests
    test_files = [
        "tests/unit/middleware/test_cors_middleware.py",
        "tests/unit/middleware/test_security_headers_middleware.py",
        "tests/unit/middleware/test_rate_limit_middleware.py",
    ]
    
    print("Running security middleware tests...")
    result = subprocess.run(["pytest", "-v"] + test_files, env=os.environ)
    
    # Return the exit code
    return result.returncode

if __name__ == "__main__":
    sys.exit(main())
