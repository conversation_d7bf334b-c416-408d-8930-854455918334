"""
Prompt Manager Service

This service provides a centralized way to manage, version, and A/B test prompts
used throughout the application. It exposes a REST API for CRUD operations and
a WebSocket endpoint for real-time updates.

Usage:
    from fastapi import FastAPI
    from services.prompt_mgr import app as prompt_mgr_app

    app = FastAPI()
    app.mount("/prompt-mgr", prompt_mgr_app)
"""

from fastapi import FastAPI, Depends
from fastapi.middleware.cors import CORSMiddleware

from services.prompt_mgr.routes import router

# Create FastAPI sub-app
app = FastAPI(
    title="Prompt Manager",
    description="Service for managing, versioning, and A/B testing prompts",
    version="0.1.0",
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # In production, restrict this to specific origins
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Include router
app.include_router(router)

# Export the app for mounting in the main application
__all__ = ["app"]
