"""
Tests for the Prompt Manager routes.
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi import FastAPI
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

from services.prompt_mgr.models import Base, Prompt, Version
from services.prompt_mgr.routes import router
from services.prompt_mgr.db import get_db


# Create a test database
TEST_DATABASE_URL = "sqlite+aiosqlite:///:memory:"


# Create a test engine and session
@pytest.fixture
async def engine():
    """Create an in-memory SQLite engine for testing."""
    engine = create_async_engine(TEST_DATABASE_URL, echo=False)
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.create_all)
    
    yield engine
    
    async with engine.begin() as conn:
        await conn.run_sync(Base.metadata.drop_all)
    
    await engine.dispose()


@pytest.fixture
async def session(engine):
    """Create a new database session for testing."""
    async_session_factory = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False
    )
    
    async with async_session_factory() as session:
        yield session


@pytest.fixture
async def app(session):
    """Create a test FastAPI application."""
    app = FastAPI()
    
    # Override the get_db dependency
    async def override_get_db():
        try:
            yield session
        finally:
            pass
    
    app.dependency_overrides[get_db] = override_get_db
    
    # Include the router
    app.include_router(router)
    
    return app


@pytest.fixture
async def client(app):
    """Create a test client for the FastAPI application."""
    async with AsyncClient(app=app, base_url="http://test") as client:
        yield client


@pytest.mark.asyncio
async def test_create_prompt(client):
    """Test creating a prompt."""
    # Create a prompt
    response = await client.post(
        "/api/v1/prompts",
        json={
            "key": "test_prompt",
            "description": "Test prompt description",
            "tags": ["test", "prompt"],
            "version": {
                "content": "This is a test prompt: {{variable}}",
                "metadata": {"model": "gpt-4", "temperature": 0.7},
                "created_by": "<EMAIL>"
            }
        }
    )
    
    # Check response
    assert response.status_code == 201
    data = response.json()
    assert data["key"] == "test_prompt"
    assert data["description"] == "Test prompt description"
    assert data["tags"] == ["test", "prompt"]
    assert data["is_active"] is True
    assert data["latest_version"]["content"] == "This is a test prompt: {{variable}}"
    assert data["latest_version"]["metadata"] == {"model": "gpt-4", "temperature": 0.7}
    assert data["latest_version"]["created_by"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_get_prompt(client, session):
    """Test getting a prompt."""
    # Create a prompt
    prompt = Prompt(
        key="test_prompt",
        description="Test prompt description",
        tags=["test", "prompt"],
        is_active=True
    )
    
    session.add(prompt)
    await session.flush()
    
    # Create a version
    version = Version(
        prompt_id=prompt.id,
        version=1,
        content="This is a test prompt: {{variable}}",
        metadata={"model": "gpt-4", "temperature": 0.7},
        created_by="<EMAIL>"
    )
    
    session.add(version)
    await session.commit()
    
    # Get the prompt
    response = await client.get("/api/v1/prompts/test_prompt")
    
    # Check response
    assert response.status_code == 200
    data = response.json()
    assert data["key"] == "test_prompt"
    assert data["description"] == "Test prompt description"
    assert data["tags"] == ["test", "prompt"]
    assert data["is_active"] is True
    assert data["latest_version"]["content"] == "This is a test prompt: {{variable}}"
    assert data["latest_version"]["metadata"] == {"model": "gpt-4", "temperature": 0.7}
    assert data["latest_version"]["created_by"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_update_prompt(client, session):
    """Test updating a prompt."""
    # Create a prompt
    prompt = Prompt(
        key="test_prompt",
        description="Test prompt description",
        tags=["test", "prompt"],
        is_active=True
    )
    
    session.add(prompt)
    await session.flush()
    
    # Create a version
    version = Version(
        prompt_id=prompt.id,
        version=1,
        content="This is a test prompt: {{variable}}",
        metadata={"model": "gpt-4", "temperature": 0.7},
        created_by="<EMAIL>"
    )
    
    session.add(version)
    await session.commit()
    
    # Update the prompt
    response = await client.put(
        f"/api/v1/prompts/{prompt.id}",
        json={
            "description": "Updated description",
            "tags": ["test", "prompt", "updated"],
            "new_version": {
                "content": "This is an updated test prompt: {{variable}}",
                "metadata": {"model": "gpt-4", "temperature": 0.5},
                "created_by": "<EMAIL>"
            }
        }
    )
    
    # Check response
    assert response.status_code == 200
    data = response.json()
    assert data["description"] == "Updated description"
    assert data["tags"] == ["test", "prompt", "updated"]
    assert data["latest_version"]["version"] == 2
    assert data["latest_version"]["content"] == "This is an updated test prompt: {{variable}}"
    assert data["latest_version"]["metadata"] == {"model": "gpt-4", "temperature": 0.5}
    assert data["latest_version"]["created_by"] == "<EMAIL>"


@pytest.mark.asyncio
async def test_delete_prompt(client, session):
    """Test deleting a prompt."""
    # Create a prompt
    prompt = Prompt(
        key="test_prompt",
        description="Test prompt description",
        tags=["test", "prompt"],
        is_active=True
    )
    
    session.add(prompt)
    await session.flush()
    
    # Create a version
    version = Version(
        prompt_id=prompt.id,
        version=1,
        content="This is a test prompt: {{variable}}",
        metadata={"model": "gpt-4", "temperature": 0.7},
        created_by="<EMAIL>"
    )
    
    session.add(version)
    await session.commit()
    
    # Delete the prompt
    response = await client.delete(f"/api/v1/prompts/{prompt.id}")
    
    # Check response
    assert response.status_code == 204
    
    # Check that the prompt was deleted
    result = await session.execute(
        "SELECT * FROM prompts WHERE id = :id",
        {"id": prompt.id}
    )
    assert result.fetchone() is None
    
    # Check that the version was deleted
    result = await session.execute(
        "SELECT * FROM prompt_versions WHERE prompt_id = :prompt_id",
        {"prompt_id": prompt.id}
    )
    assert result.fetchone() is None


@pytest.mark.asyncio
async def test_get_nonexistent_prompt(client):
    """Test getting a nonexistent prompt."""
    response = await client.get("/api/v1/prompts/nonexistent")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_update_nonexistent_prompt(client):
    """Test updating a nonexistent prompt."""
    response = await client.put(
        "/api/v1/prompts/999",
        json={
            "description": "Updated description",
            "tags": ["test", "prompt", "updated"]
        }
    )
    assert response.status_code == 404
    assert "not found" in response.json()["detail"]


@pytest.mark.asyncio
async def test_delete_nonexistent_prompt(client):
    """Test deleting a nonexistent prompt."""
    response = await client.delete("/api/v1/prompts/999")
    assert response.status_code == 404
    assert "not found" in response.json()["detail"]
