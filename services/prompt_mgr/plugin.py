"""
LangGraph plugin for the Prompt Manager service.

This module provides a PromptNode class that can be used in LangGraph workflows
to fetch prompts from the Prompt Manager service.
"""

import logging
import httpx
import json
from typing import Dict, Any, Optional, List, Union, Callable
from pydantic import BaseModel, Field
import os
import random
from datetime import datetime

from langgraph.graph import StateGraph

# Set up logging
logger = logging.getLogger(__name__)

# Default base URL for the Prompt Manager service
DEFAULT_BASE_URL = "http://localhost:8000/prompt-mgr/api/v1"


class PromptNodeConfig(BaseModel):
    """Configuration for the PromptNode."""
    base_url: str = Field(
        default_factory=lambda: os.getenv("PROMPT_MGR_URL", DEFAULT_BASE_URL),
        description="Base URL for the Prompt Manager service"
    )
    timeout: float = Field(
        default=5.0,
        description="Timeout for HTTP requests in seconds"
    )
    fallback_content: Optional[Dict[str, str]] = Field(
        default=None,
        description="Fallback prompt content to use if the service is unavailable"
    )
    experiment_id: Optional[int] = Field(
        default=None,
        description="Experiment ID for A/B testing"
    )


class PromptNode:
    """
    LangGraph node for fetching prompts from the Prompt Manager service.
    
    This node can be used in LangGraph workflows to fetch prompts by key.
    It supports template variable substitution and A/B testing.
    
    Example:
        ```python
        from services.prompt_mgr.plugin import PromptNode
        from langgraph.graph import StateGraph
        
        # Create a PromptNode
        prompt_node = PromptNode()
        
        # Add it to a graph
        graph = StateGraph()
        graph.add_node("get_prompt", prompt_node)
        
        # Use it in a workflow
        result = await graph.ainvoke({
            "prompt_key": "research_assistant",
            "prompt_vars": {"topic": "climate change"}
        })
        ```
    """
    
    def __init__(
        self,
        config: Optional[PromptNodeConfig] = None,
        http_client: Optional[httpx.AsyncClient] = None
    ):
        """
        Initialize the PromptNode.
        
        Args:
            config: Configuration for the node
            http_client: Optional HTTP client to use for requests
        """
        self.config = config or PromptNodeConfig()
        self.http_client = http_client
        self._client_owned = http_client is None
    
    async def __call__(
        self,
        state: Dict[str, Any],
        config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Fetch a prompt from the Prompt Manager service.
        
        Args:
            state: The current state
            config: Configuration for the node
            
        Returns:
            Updated state with the prompt
            
        The state should contain:
            - prompt_key: The key of the prompt to fetch
            - prompt_vars: Optional variables to substitute in the prompt
            
        The updated state will contain:
            - prompt: The fetched prompt with variables substituted
            - prompt_metadata: Metadata about the prompt
        """
        # Get prompt key from state
        prompt_key = state.get("prompt_key")
        if not prompt_key:
            logger.error("No prompt_key provided in state")
            return {
                "error": "No prompt_key provided in state",
                "prompt": None,
                "prompt_metadata": None
            }
        
        # Get variables from state
        prompt_vars = state.get("prompt_vars", {})
        
        # Get experiment ID from state or config
        experiment_id = state.get("experiment_id", self.config.experiment_id)
        
        try:
            # Fetch prompt
            prompt_content, prompt_metadata = await self._fetch_prompt(
                prompt_key, experiment_id
            )
            
            # Substitute variables
            prompt = self._substitute_variables(prompt_content, prompt_vars)
            
            # Return updated state
            return {
                "prompt": prompt,
                "prompt_metadata": prompt_metadata
            }
        except Exception as e:
            logger.exception(f"Error fetching prompt '{prompt_key}': {str(e)}")
            
            # Use fallback if available
            if self.config.fallback_content and prompt_key in self.config.fallback_content:
                fallback = self.config.fallback_content[prompt_key]
                prompt = self._substitute_variables(fallback, prompt_vars)
                
                return {
                    "prompt": prompt,
                    "prompt_metadata": {"source": "fallback"},
                    "error": str(e)
                }
            
            # Return error
            return {
                "error": f"Error fetching prompt: {str(e)}",
                "prompt": None,
                "prompt_metadata": None
            }
    
    async def _fetch_prompt(
        self,
        prompt_key: str,
        experiment_id: Optional[int] = None
    ) -> tuple[str, Dict[str, Any]]:
        """
        Fetch a prompt from the Prompt Manager service.
        
        Args:
            prompt_key: The key of the prompt to fetch
            experiment_id: Optional experiment ID for A/B testing
            
        Returns:
            Tuple of (prompt_content, prompt_metadata)
        """
        # Create HTTP client if needed
        if self.http_client is None:
            self.http_client = httpx.AsyncClient(timeout=self.config.timeout)
            self._client_owned = True
        
        try:
            # Build URL
            url = f"{self.config.base_url}/prompts/{prompt_key}"
            if experiment_id:
                url += f"?experiment_id={experiment_id}"
            
            # Make request
            response = await self.http_client.get(url)
            response.raise_for_status()
            
            # Parse response
            data = response.json()
            
            # Extract content and metadata
            content = data["latest_version"]["content"]
            metadata = {
                "id": data["id"],
                "key": data["key"],
                "version": data["latest_version"]["version"],
                "version_id": data["latest_version"]["id"],
                "created_at": data["latest_version"]["created_at"],
                "created_by": data["latest_version"]["created_by"],
                "metadata": data["latest_version"]["metadata"] or {}
            }
            
            return content, metadata
        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error fetching prompt '{prompt_key}': {e.response.status_code} {e.response.text}")
            raise
        except httpx.RequestError as e:
            logger.error(f"Request error fetching prompt '{prompt_key}': {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error fetching prompt '{prompt_key}': {str(e)}")
            raise
    
    def _substitute_variables(self, content: str, variables: Dict[str, Any]) -> str:
        """
        Substitute variables in a prompt template.
        
        Args:
            content: The prompt template
            variables: Variables to substitute
            
        Returns:
            The prompt with variables substituted
        """
        result = content
        
        # Replace {{variable}} with value
        for key, value in variables.items():
            result = result.replace(f"{{{{{key}}}}}", str(value))
        
        return result
    
    async def close(self):
        """Close the HTTP client if owned by this instance."""
        if self._client_owned and self.http_client is not None:
            await self.http_client.aclose()
            self.http_client = None
