README.md
pyproject.toml
setup.py
backend/__init__.py
shared/__init__.py
src/pi_lawyer/__init__.py
src/pi_lawyer/config.py
src/pi_lawyer/document_worker.py
src/pi_lawyer/agents/__init__.py
src/pi_lawyer/agents/_stubs.py
src/pi_lawyer/agents/base_agent.py
src/pi_lawyer/agents/config.py
src/pi_lawyer/agents/copilotkit_runtime.py
src/pi_lawyer/agents/deadline_agent.py
src/pi_lawyer/agents/document_agent.py
src/pi_lawyer/agents/echo_agent.py
src/pi_lawyer/agents/intake_agent.py
src/pi_lawyer/agents/router.py
src/pi_lawyer/agents/graph/__init__.py
src/pi_lawyer/agents/graph/builder.py
src/pi_lawyer/agents/graph/builder_update.py
src/pi_lawyer/agents/graph/finish.py
src/pi_lawyer/agents/graph/tests/__init__.py
src/pi_lawyer/agents/graph/tests/test_finish_node.py
src/pi_lawyer/agents/graph/tests/test_graph.py
src/pi_lawyer/agents/insights/__init__.py
src/pi_lawyer/agents/insights/supervisor/__init__.py
src/pi_lawyer/agents/insights/supervisor/agent.py
src/pi_lawyer/agents/insights/supervisor/router.py
src/pi_lawyer/agents/insights/supervisor/schema.py
src/pi_lawyer/agents/insights/supervisor/tests/__init__.py
src/pi_lawyer/agents/insights/supervisor/tests/test_supervisor.py
src/pi_lawyer/agents/interactive/__init__.py
src/pi_lawyer/agents/interactive/intake/__init__.py
src/pi_lawyer/agents/interactive/intake/agent.py
src/pi_lawyer/agents/interactive/intake/routers.py
src/pi_lawyer/agents/interactive/intake/tasks.py
src/pi_lawyer/agents/tests/__init__.py
src/pi_lawyer/agents/tests/conftest.py
src/pi_lawyer/api/__init__.py
src/pi_lawyer/api/authored_document_embedding.py
src/pi_lawyer/api/copilotkit_route.py
src/pi_lawyer/api/document_route.py
src/pi_lawyer/api/document_route_variables_update.py
src/pi_lawyer/api/health_api.py
src/pi_lawyer/api/intake_route.py
src/pi_lawyer/api/main.py
src/pi_lawyer/api/research_route.py
src/pi_lawyer/api/runtime.py
src/pi_lawyer/api/runtime_old.py
src/pi_lawyer/api/task_route.py
src/pi_lawyer/auth/__init__.py
src/pi_lawyer/auth/auth_helpers.py
src/pi_lawyer/auth/jwt_auth.py
src/pi_lawyer/auth/middleware.py
src/pi_lawyer/auth/rbac.py
src/pi_lawyer/auth/tenant.py
src/pi_lawyer/config/__init__.py
src/pi_lawyer/config/cli.py
src/pi_lawyer/config/settings.py
src/pi_lawyer/config/utils.py
src/pi_lawyer/db/__init__.py
src/pi_lawyer/db/client.py
src/pi_lawyer/db/database.py
src/pi_lawyer/db/mock_auth.py
src/pi_lawyer/db/pinecone_client.py
src/pi_lawyer/db/supabase_client.py
src/pi_lawyer/documents/__init__.py
src/pi_lawyer/documents/embeddings.py
src/pi_lawyer/documents/manager.py
src/pi_lawyer/documents/processor.py
src/pi_lawyer/documents/storage.py
src/pi_lawyer/services/__init__.py
src/pi_lawyer/services/authored_document_embedding_service.py
src/pi_lawyer/services/authored_document_worker.py
src/pi_lawyer/services/circuit_breaker.py
src/pi_lawyer/services/deadline_extraction_service.py
src/pi_lawyer/services/document_analysis_service.py
src/pi_lawyer/services/document_classifier_service.py
src/pi_lawyer/services/document_embedding_utils.py
src/pi_lawyer/services/document_parser_service.py
src/pi_lawyer/services/document_processing_queue.py
src/pi_lawyer/services/document_processing_transaction.py
src/pi_lawyer/services/document_summarization.py
src/pi_lawyer/services/document_worker.py
src/pi_lawyer/services/error_classification.py
src/pi_lawyer/services/health_service.py
src/pi_lawyer/services/metrics_collector.py
src/pi_lawyer/services/redis_lock_service.py
src/pi_lawyer/services/redis_queue_service.py
src/pi_lawyer/services/task_embedding_service.py
src/pi_lawyer/services/tenant_document_embedding_service.py
src/pi_lawyer/services/services/__init__.py
src/pi_lawyer/services/services/authored_document_embedding_service.py
src/pi_lawyer/services/services/authored_document_worker.py
src/pi_lawyer/services/services/circuit_breaker.py
src/pi_lawyer/services/services/deadline_extraction_service.py
src/pi_lawyer/services/services/document_analysis_service.py
src/pi_lawyer/services/services/document_classifier_service.py
src/pi_lawyer/services/services/document_embedding_utils.py
src/pi_lawyer/services/services/document_parser_service.py
src/pi_lawyer/services/services/document_processing_queue.py
src/pi_lawyer/services/services/document_processing_transaction.py
src/pi_lawyer/services/services/document_summarization.py
src/pi_lawyer/services/services/document_worker.py
src/pi_lawyer/services/services/error_classification.py
src/pi_lawyer/services/services/health_service.py
src/pi_lawyer/services/services/metrics_collector.py
src/pi_lawyer/services/services/redis_lock_service.py
src/pi_lawyer/services/services/redis_queue_service.py
src/pi_lawyer/services/services/task_embedding_service.py
src/pi_lawyer/services/services/tenant_document_embedding_service.py
src/pi_lawyer/shared/__init__.py
src/pi_lawyer/shared/core/__init__.py
src/pi_lawyer/shared/core/state.py
src/pi_lawyer/shared/core/llm/__init__.py
src/pi_lawyer/shared/core/llm/api.py
src/pi_lawyer/shared/core/llm/base.py
src/pi_lawyer/shared/core/llm/config.py
src/pi_lawyer/shared/core/llm/factory.py
src/pi_lawyer/shared/core/llm/openai.py
src/pi_lawyer/shared/core/llm/voyage.py
src/pi_lawyer/shared/core/llm/admin/__init__.py
src/pi_lawyer/shared/core/llm/admin/keys.py
src/pi_lawyer/shared/core/llm/admin/models.py
src/pi_lawyer/shared/core/llm/admin/prompts.py
src/pi_lawyer/shared/core/llm/embeddings/__init__.py
src/pi_lawyer/shared/core/llm/embeddings/api.py
src/pi_lawyer/shared/core/llm/embeddings/base.py
src/pi_lawyer/shared/core/llm/providers/__init__.py
src/pi_lawyer/shared/core/llm/providers/anthropic.py
src/pi_lawyer/shared/core/llm/providers/gemini.py
src/pi_lawyer/shared/core/llm/providers/groq.py
src/pi_lawyer/shared/core/llm/providers/mock.py
src/pi_lawyer/shared/core/llm/providers/openai.py
src/pi_lawyer/shared/core/tools/__init__.py
src/pi_lawyer/shared/core/tools/async_job.py
src/pi_lawyer/shared/core/tools/base.py
src/pi_lawyer/shared/core/tools/enqueue_async_job.py
src/pi_lawyer/shared/core/tools/executor.py
src/pi_lawyer/shared/core/tools/schema.py
src/pi_lawyer/shared/core/tools/tests/__init__.py
src/pi_lawyer/shared/core/tools/tests/test_async_job.py
src/pi_lawyer/utils/__init__.py
src/pi_lawyer/utils/check_gcs.py
src/pi_lawyer/utils/demonstrate_paths.py
src/pi_lawyer/utils/gemini_client.py
src/pi_lawyer/utils/logging.py
src/pi_lawyer/utils/query_classifier.py
src/pi_lawyer/utils/storage_utils.py
src/pi_lawyer/utils/structured_logging.py
src/pi_lawyer/utils/telemetry.py
src/pi_lawyer/utils/template_utils.py
src/pi_lawyer/utils/test_gcs_structure.py
src/pi_lawyer/utils/voyage_embeddings.py
src/pi_lawyer/utils/voyage_reranker.py
src/pi_lawyer_ai.egg-info/PKG-INFO
src/pi_lawyer_ai.egg-info/SOURCES.txt
src/pi_lawyer_ai.egg-info/dependency_links.txt
src/pi_lawyer_ai.egg-info/requires.txt
src/pi_lawyer_ai.egg-info/top_level.txt
tests/test_ai_intake.py
tests/test_check_gcs.py
tests/test_circuit_breaker.py
tests/test_client_intake.py
tests/test_database.py
tests/test_demonstrate_paths.py
tests/test_document_worker.py
tests/test_error_classification.py
tests/test_gemini_client.py
tests/test_logging.py
tests/test_query_classifier.py
tests/test_rbac.py
tests/test_research_access.py
tests/test_research_agent.py
tests/test_research_state.py
tests/test_simple.py
tests/test_storage_utils.py
tests/test_structured_logging.py
tests/test_supabase_connection.py
tests/test_template_utils.py
tests/test_test_gcs_structure.py
tests/test_voyage_reranker.py