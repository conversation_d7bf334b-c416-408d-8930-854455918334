"""
Embedding API

This module provides a unified API for getting embeddings from various providers.
"""

import logging
from typing import Dict, List, Optional, Type

from ..base import LLMError
from ..config import EmbeddingConfig, LLMProvider
from .base import BaseEmbeddingClient

# Set up logging
logger = logging.getLogger(__name__)


# Cache for embedding clients
_embedding_clients: Dict[str, BaseEmbeddingClient] = {}


def get_embedding_client(
    config: Optional[EmbeddingConfig] = None,
    force_new: bool = False
) -> BaseEmbeddingClient:
    """
    Get an embedding client for the specified configuration.
    
    Args:
        config: Configuration for the embedding client
        force_new: Whether to force creation of a new client
        
    Returns:
        An embedding client
        
    Raises:
        ValueError: If the provider is not supported
    """
    if config is None:
        config = EmbeddingConfig()
    
    # Create a cache key from the configuration
    provider = config.provider
    if isinstance(provider, LLMProvider):
        provider = provider.value
    
    cache_key = f"{provider}:{config.model}:{config.api_base_url or 'default'}"
    
    # Return cached client if available and not forcing new
    if not force_new and cache_key in _embedding_clients:
        return _embedding_clients[cache_key]
    
    # Import provider implementations
    from .openai import OpenAIEmbeddingClient
    from .voyage import VoyageEmbeddingClient
    
    # Map providers to client classes
    provider_map: Dict[LLMProvider, Type[BaseEmbeddingClient]] = {
        LLMProvider.OPENAI: OpenAIEmbeddingClient,
        LLMProvider.VOYAGE: VoyageEmbeddingClient,
    }
    
    # Get the client class for the provider
    provider_enum = config.provider
    if isinstance(provider_enum, str):
        try:
            provider_enum = LLMProvider(provider_enum)
        except ValueError:
            raise ValueError(f"Unsupported provider: {provider_enum}")
    
    client_class = provider_map.get(provider_enum)
    if client_class is None:
        raise ValueError(f"Unsupported embedding provider: {provider_enum}")
    
    # Create the client
    client = client_class(config=config)
    
    # Cache the client
    _embedding_clients[cache_key] = client
    
    return client


async def get_embeddings(
    texts: List[str],
    config: Optional[EmbeddingConfig] = None,
    fallback_configs: Optional[List[EmbeddingConfig]] = None,
    **kwargs
) -> List[List[float]]:
    """
    Get embeddings for the given texts with fallback support.
    
    Args:
        texts: The texts to get embeddings for
        config: Configuration for the primary embedding provider
        fallback_configs: Configurations for fallback embedding providers
        **kwargs: Additional parameters for the embedding provider
        
    Returns:
        A list of embeddings, one for each text
        
    Raises:
        LLMError: If all embedding requests fail
    """
    if config is None:
        config = EmbeddingConfig()
    
    # Try the primary embedding provider
    try:
        client = get_embedding_client(config)
        return await client.get_embeddings(
            texts=texts,
            model=config.model,
            **kwargs
        )
    except LLMError as e:
        logger.warning(
            f"Primary embedding request failed: {str(e)}",
            extra={"error": str(e), "provider": config.provider, "model": config.model}
        )
        
        # Try fallback embedding providers if available
        if fallback_configs:
            for fallback_config in fallback_configs:
                try:
                    client = get_embedding_client(fallback_config)
                    embeddings = await client.get_embeddings(
                        texts=texts,
                        model=fallback_config.model,
                        **kwargs
                    )
                    
                    logger.info(
                        f"Fallback embedding request succeeded",
                        extra={"provider": fallback_config.provider, "model": fallback_config.model}
                    )
                    
                    return embeddings
                except LLMError as e:
                    logger.warning(
                        f"Fallback embedding request failed: {str(e)}",
                        extra={"error": str(e), "provider": fallback_config.provider, "model": fallback_config.model}
                    )
        
        # If all embedding providers fail, raise the original error
        raise
