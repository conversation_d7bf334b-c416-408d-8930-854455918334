#
For
Gemini
HTTP
LLM
PyJWT>=2.9.0
PyYAML==6.0.2
SDK
SQLAlchemy==2.0.41
aiohttp>=3.11.18,<4
annotated-types==0.7.0
anthropic==0.26.0
anyio==4.9.0
async
async-timeout==4.0.3
backoff>=2.2.1
bcrypt==4.3.0
certifi==2025.4.26
cffi==1.17.1
charset-normalizer==3.4.2
click==8.2.0
client
copilotkit==0.1.46
cryptography==44.0.3
distro==1.9.0
ecdsa==0.19.1
email-validator
exceptiongroup==1.3.0
fastapi==0.115.12
google-cloud-storage>=2.14.0
google-generativeai==0.5.4
h11==0.16.0
httpcore==1.0.9
httpx==0.25.2
httpx==0.28.1
idna==3.10
in
jiter==0.9.0
jsonpatch==1.33
jsonpointer==3.0.0
langchain-core==0.3.60
langchain-openai==0.3.17
langchain-text-splitters==0.3.8
langchain==0.3.25
langgraph-checkpoint==2.0.25
langgraph-prebuilt==0.1.8
langgraph-sdk==0.1.69
langgraph==0.2.50
langsmith==0.3.42
langsmith>=0.3.42
numpy==2.2.5
numpy>=1.24.0
openai==1.78.1
openai>=1.3.7
orjson==3.10.18
ormsgpack==1.9.1
packaging==24.2
pandas>=2.0.0
partialjson==0.0.8
passlib==1.7.4
pinecone==6.0.2
pinecone>=5.1.0
provider
pyasn1==0.4.8
pycparser==2.22
pydantic==2.11.4
pydantic[email]
pydantic[email]==2.11.4
pydantic_core==2.33.2
pytest-asyncio==0.21.1
pytest-asyncio==0.26.0
pytest-cov==4.1.0
pytest-mock==3.12.0
pytest-mock==3.14.0
pytest==7.4.3
pytest==8.3.5
python-dateutil==2.8.2
python-dotenv==1.1.0
python-jose==3.4.0
python-multipart>=0.0.6
regex==2024.11.6
requests-toolbelt==1.0.0
requests==2.32.3
rsa==4.9.1
scipy>=1.10.0
sentence-transformers>=2.2.2
six==1.17.0
sniffio==1.3.1
starlette==0.46.2
supabase>=2.15,<3
supabase>=2.3.0
tenacity==9.1.2
testing
tests
tiktoken==0.9.0
toml==0.10.2
tqdm==4.67.1
tqdm>=4.66.2
typing-inspection==0.4.0
typing_extensions==4.13.2
urllib3==2.4.0
used
uvicorn==0.34.2
voyageai==0.3.2
voyageai>=0.1.6
xxhash==3.5.0
zstandard==0.23.0
