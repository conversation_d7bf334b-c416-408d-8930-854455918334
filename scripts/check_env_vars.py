#!/usr/bin/env python
"""
Environment Variable Checker for PI Lawyer AI

This script checks if all required environment variables are set and provides
clear error messages for missing variables. It's designed to be run before
starting the FastAPI server to ensure all necessary configuration is in place.
"""

import os
import sys
from pathlib import Path
from dotenv import load_dotenv
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger("env_checker")

# Define required environment variables by category
REQUIRED_VARS = {
    "API Keys": [
        "OPENAI_API_KEY",
        "VOYAGE_API_KEY",
        "PINECONE_API_KEY",
    ],
    "Database": [
        "PINECONE_ENVIRONMENT",
        "PINECONE_INDEX_NAME",
    ],
    "Authentication": [
        "SUPABASE_URL",
        "SUPABASE_KEY",
    ],
    "CopilotKit": [
        "CPK_ENDPOINT_SECRET",
    ],
}

# Define optional environment variables
OPTIONAL_VARS = {
    "Monitoring": [
        "LANGSMITH_API_KEY",
        "LANGSMITH_PROJECT",
    ],
    "Application": [
        "DEBUG",
        "LOG_LEVEL",
        "CORS_ORIGINS",
        "PORT",
        "HOST",
    ],
}

def load_env_file():
    """Load environment variables from .env file."""
    # Find and load .env file explicitly
    env_path = Path(__file__).parent.parent / '.env'
    logger.info(f"Loading environment variables from: {env_path}")

    if not env_path.exists():
        logger.warning(f".env file not found at {env_path}")
        return False

    # Load .env and override any existing vars to pick up updated values
    load_dotenv(dotenv_path=env_path, override=True)
    logger.info("Environment variables loaded successfully")
    return True

def check_required_vars():
    """Check if all required environment variables are set."""
    missing_vars = {}
    
    for category, vars_list in REQUIRED_VARS.items():
        missing = [var for var in vars_list if not os.getenv(var)]
        if missing:
            missing_vars[category] = missing
    
    return missing_vars

def check_optional_vars():
    """Check if optional environment variables are set and log warnings."""
    missing_vars = {}
    
    for category, vars_list in OPTIONAL_VARS.items():
        missing = [var for var in vars_list if not os.getenv(var)]
        if missing:
            missing_vars[category] = missing
    
    return missing_vars

def print_env_summary(include_values=False):
    """Print a summary of environment variables."""
    print("\n=== Environment Variables Summary ===\n")
    
    # Print required variables
    print("Required Variables:")
    for category, vars_list in REQUIRED_VARS.items():
        print(f"  {category}:")
        for var in vars_list:
            value = os.getenv(var)
            status = "✓" if value else "✗"
            
            if include_values and value:
                # Mask sensitive values
                if any(sensitive in var.lower() for sensitive in ['key', 'password', 'secret', 'token']):
                    if len(value) > 8:
                        display_value = f"{value[:4]}...{value[-4:]}"
                    else:
                        display_value = "********"
                else:
                    display_value = value
                
                print(f"    {status} {var}: {display_value}")
            else:
                print(f"    {status} {var}")
    
    # Print optional variables
    print("\nOptional Variables:")
    for category, vars_list in OPTIONAL_VARS.items():
        print(f"  {category}:")
        for var in vars_list:
            value = os.getenv(var)
            status = "✓" if value else "-"
            
            if include_values and value:
                # Mask sensitive values
                if any(sensitive in var.lower() for sensitive in ['key', 'password', 'secret', 'token']):
                    if len(value) > 8:
                        display_value = f"{value[:4]}...{value[-4:]}"
                    else:
                        display_value = "********"
                else:
                    display_value = value
                
                print(f"    {status} {var}: {display_value}")
            else:
                print(f"    {status} {var}")

def main():
    """Main function to check environment variables."""
    # Load environment variables
    load_env_file()
    
    # Check required variables
    missing_required = check_required_vars()
    
    # Check optional variables
    missing_optional = check_optional_vars()
    
    # Print summary
    print_env_summary(include_values=True)
    
    # Print warnings for missing variables
    if missing_optional:
        print("\n=== Missing Optional Variables ===\n")
        for category, vars_list in missing_optional.items():
            print(f"{category}:")
            for var in vars_list:
                print(f"  - {var}")
        print("\nThese variables are optional but recommended for full functionality.")
    
    # Exit with error if required variables are missing
    if missing_required:
        print("\n=== ERROR: Missing Required Variables ===\n")
        for category, vars_list in missing_required.items():
            print(f"{category}:")
            for var in vars_list:
                print(f"  - {var}")
        
        print("\nPlease set these variables in your .env file before starting the server.")
        sys.exit(1)
    
    print("\nAll required environment variables are set. You can start the server.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
