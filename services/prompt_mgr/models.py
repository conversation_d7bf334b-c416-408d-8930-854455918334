"""
SQLAlchemy models for the Prompt Manager service.

This module defines the database models for prompts, versions, and experiments.
"""

from datetime import datetime
from typing import List, Dict, Optional, Any
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, 
    ForeignKey, Boolean, JSON, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, validates
import uuid

Base = declarative_base()


class Prompt(Base):
    """
    Prompt model representing a prompt template with metadata.
    
    A prompt has multiple versions, with the latest version being the one
    currently in use unless overridden by an experiment.
    """
    __tablename__ = "prompts"

    id = Column(Integer, primary_key=True, autoincrement=True)
    key = Column(String(255), unique=True, nullable=False, index=True)
    description = Column(Text, nullable=True)
    tags = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)

    # Relationships
    versions = relationship("Version", back_populates="prompt", cascade="all, delete-orphan")
    experiments = relationship("Experiment", secondary="experiment_prompts", back_populates="prompts")

    @validates('key')
    def validate_key(self, key, value):
        """Validate that the key is a valid identifier."""
        if not value or not value.strip():
            raise ValueError("Prompt key cannot be empty")
        return value.strip()

    def __repr__(self):
        return f"<Prompt(id={self.id}, key='{self.key}')>"


class Version(Base):
    """
    Version model representing a specific version of a prompt.
    
    Each version contains the actual prompt content and metadata.
    """
    __tablename__ = "prompt_versions"

    id = Column(Integer, primary_key=True, autoincrement=True)
    prompt_id = Column(Integer, ForeignKey("prompts.id", ondelete="CASCADE"), nullable=False)
    version = Column(Integer, nullable=False)
    content = Column(Text, nullable=False)
    metadata = Column(JSON, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    created_by = Column(String(255), nullable=True)

    # Relationships
    prompt = relationship("Prompt", back_populates="versions")
    
    # Ensure each prompt has unique version numbers
    __table_args__ = (
        UniqueConstraint('prompt_id', 'version', name='uix_prompt_version'),
    )

    @validates('content')
    def validate_content(self, key, value):
        """Validate that the content is not empty."""
        if not value or not value.strip():
            raise ValueError("Prompt content cannot be empty")
        return value

    def __repr__(self):
        return f"<Version(id={self.id}, prompt_id={self.prompt_id}, version={self.version})>"


# Association table for many-to-many relationship between Experiment and Prompt
class ExperimentPrompt(Base):
    """Association table for experiments and prompts with traffic allocation."""
    __tablename__ = "experiment_prompts"

    experiment_id = Column(Integer, ForeignKey("prompt_experiments.id", ondelete="CASCADE"), primary_key=True)
    prompt_id = Column(Integer, ForeignKey("prompts.id", ondelete="CASCADE"), primary_key=True)
    version_id = Column(Integer, ForeignKey("prompt_versions.id", ondelete="CASCADE"), nullable=False)
    traffic_allocation = Column(Integer, default=0, nullable=False)  # Percentage of traffic (0-100)


class Experiment(Base):
    """
    Experiment model for A/B testing prompts.
    
    An experiment can include multiple prompts with different versions and
    traffic allocations for A/B testing.
    """
    __tablename__ = "prompt_experiments"

    id = Column(Integer, primary_key=True, autoincrement=True)
    name = Column(String(255), unique=True, nullable=False)
    description = Column(Text, nullable=True)
    is_active = Column(Boolean, default=True, nullable=False)
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    created_by = Column(String(255), nullable=True)
    
    # Relationships
    prompts = relationship("Prompt", secondary="experiment_prompts", back_populates="experiments")

    @validates('name')
    def validate_name(self, key, value):
        """Validate that the name is not empty."""
        if not value or not value.strip():
            raise ValueError("Experiment name cannot be empty")
        return value.strip()

    def __repr__(self):
        return f"<Experiment(id={self.id}, name='{self.name}')>"
