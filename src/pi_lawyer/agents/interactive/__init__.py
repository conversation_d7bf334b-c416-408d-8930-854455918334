"""
Interactive Agents Package

This package contains agents that interact with users in real-time.
These agents typically have quick response times (≤5 seconds).

Available agents:
- Intake Agent: Collects client information and creates new cases
- Research Agent: Performs quick legal research queries
- Task CRUD Agent: Creates, reads, updates, and deletes tasks
- Calendar CRUD Agent: Manages calendar events
- Case CRUD Agent: Manages case information
"""

# Import intake agent
try:
    from pi_lawyer.agents.interactive.intake import (
        IntakeAgent,
        intake_client_router,
        intake_staff_router
    )
except ImportError:
    pass

__all__ = [
    "IntakeAgent",
    "intake_client_router",
    "intake_staff_router"
]
