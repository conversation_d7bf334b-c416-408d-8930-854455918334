#!/usr/bin/env python
"""
Helper script to run the FastAPI server with environment variables properly loaded.
This ensures all environment variables from .env are loaded before any imports.
"""
import os
import sys
import subprocess
from pathlib import Path
import importlib.util

def run_env_checker():
    """Run the environment variable checker script."""
    checker_path = Path(__file__).parent / 'scripts' / 'check_env_vars.py'

    if not checker_path.exists():
        print(f"Warning: Environment variable checker not found at {checker_path}")
        return False

    # Load and run the checker module
    spec = importlib.util.spec_from_file_location("check_env_vars", checker_path)
    checker_module = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(checker_module)

    # Run the checker
    try:
        result = checker_module.main()
        return result == 0
    except Exception as e:
        print(f"Error running environment variable checker: {e}")
        return False

def load_env_file():
    """Load environment variables from .env file."""
    # Find and load .env file explicitly
    env_path = Path(__file__).parent / '.env'
    print(f"Loading environment variables from: {env_path}")

    if not env_path.exists():
        print(f"Error: .env file not found at {env_path}")
        return False

    # Parse .env file and set environment variables
    env_vars = {}
    with open(env_path) as f:
        for line in f:
            line = line.strip()
            if not line or line.startswith('#'):
                continue
            try:
                key, value = line.split('=', 1)
                env_vars[key] = value
                os.environ[key] = value
            except ValueError:
                print(f"Warning: Invalid line in .env file: {line}")

    # Print loaded environment variables (excluding sensitive values)
    print("Loaded environment variables:")
    for key in env_vars:
        if any(sensitive in key.lower() for sensitive in ['key', 'password', 'secret', 'token']):
            print(f"  {key}=****")
        else:
            print(f"  {key}={env_vars[key]}")

    return True

def main():
    """Main function to run the FastAPI server."""
    # First, load environment variables
    if not load_env_file():
        print("Error: Failed to load environment variables")
        return 1

    # Then, check environment variables
    print("\nChecking environment variables...\n")
    if not run_env_checker():
        print("\nWarning: Environment variable check failed or was skipped")
        user_input = input("Do you want to continue anyway? (y/n): ")
        if user_input.lower() != 'y':
            print("Exiting...")
            return 1

    # Run the FastAPI server with the environment variables set
    print("\nStarting FastAPI server...\n")
    cmd = ["python", "-m", "uvicorn", "src.pi_lawyer.api.runtime:app", "--host", "0.0.0.0", "--port", "8000", "--reload"]

    try:
        subprocess.run(cmd, env=os.environ)
        return 0
    except KeyboardInterrupt:
        print("\nServer stopped by user")
        return 0
    except Exception as e:
        print(f"\nError starting server: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
