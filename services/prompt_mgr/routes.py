"""
API routes for the Prompt Manager service.

This module defines the REST and WebSocket endpoints for managing prompts.
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional, Any, Union
import logging

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, Query, Path, Body
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import func, desc
from pydantic import BaseModel, Field, validator

from services.prompt_mgr.models import Prompt, Version, Experiment, ExperimentPrompt
from services.prompt_mgr.db import get_db

# Set up logging
logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1")

# Pydantic models for request/response validation
class VersionCreate(BaseModel):
    """Schema for creating a new prompt version."""
    content: str = Field(..., description="The prompt template content")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Optional metadata for the prompt")
    created_by: Optional[str] = Field(None, description="User who created this version")

    @validator('content')
    def content_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError("Prompt content cannot be empty")
        return v.strip()

    class Config:
        schema_extra = {
            "example": {
                "content": "You are a helpful assistant. Answer the following question: {{question}}",
                "metadata": {"model": "gpt-4", "temperature": 0.7},
                "created_by": "<EMAIL>"
            }
        }


class PromptCreate(BaseModel):
    """Schema for creating a new prompt."""
    key: str = Field(..., description="Unique identifier for the prompt")
    description: Optional[str] = Field(None, description="Description of the prompt's purpose")
    tags: Optional[List[str]] = Field(None, description="Tags for categorizing the prompt")
    version: VersionCreate = Field(..., description="Initial version of the prompt")

    @validator('key')
    def key_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError("Prompt key cannot be empty")
        return v.strip()

    class Config:
        schema_extra = {
            "example": {
                "key": "research_assistant",
                "description": "Prompt for the research assistant role",
                "tags": ["research", "assistant"],
                "version": {
                    "content": "You are a research assistant. Find information about: {{topic}}",
                    "metadata": {"model": "gpt-4", "temperature": 0.5},
                    "created_by": "<EMAIL>"
                }
            }
        }


class PromptUpdate(BaseModel):
    """Schema for updating a prompt."""
    description: Optional[str] = Field(None, description="Updated description")
    tags: Optional[List[str]] = Field(None, description="Updated tags")
    is_active: Optional[bool] = Field(None, description="Whether the prompt is active")
    new_version: Optional[VersionCreate] = Field(None, description="New version to create")

    class Config:
        schema_extra = {
            "example": {
                "description": "Updated description for the research assistant",
                "tags": ["research", "assistant", "updated"],
                "is_active": True,
                "new_version": {
                    "content": "You are an expert research assistant. Find detailed information about: {{topic}}",
                    "metadata": {"model": "gpt-4", "temperature": 0.3},
                    "created_by": "<EMAIL>"
                }
            }
        }


class VersionResponse(BaseModel):
    """Schema for version response."""
    id: int
    version: int
    content: str
    metadata: Optional[Dict[str, Any]]
    created_at: datetime
    created_by: Optional[str]

    class Config:
        orm_mode = True


class PromptResponse(BaseModel):
    """Schema for prompt response."""
    id: int
    key: str
    description: Optional[str]
    tags: Optional[List[str]]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    latest_version: Optional[VersionResponse]

    class Config:
        orm_mode = True


class ExperimentCreate(BaseModel):
    """Schema for creating a new experiment."""
    name: str = Field(..., description="Name of the experiment")
    description: Optional[str] = Field(None, description="Description of the experiment")
    is_active: bool = Field(True, description="Whether the experiment is active")
    start_date: Optional[datetime] = Field(None, description="Start date of the experiment")
    end_date: Optional[datetime] = Field(None, description="End date of the experiment")
    created_by: Optional[str] = Field(None, description="User who created this experiment")
    prompt_versions: Dict[int, Dict[int, int]] = Field(
        ..., 
        description="Mapping of prompt_id to {version_id: traffic_allocation}"
    )

    @validator('name')
    def name_not_empty(cls, v):
        if not v or not v.strip():
            raise ValueError("Experiment name cannot be empty")
        return v.strip()

    @validator('prompt_versions')
    def validate_traffic_allocation(cls, v):
        for prompt_id, versions in v.items():
            total = sum(versions.values())
            if total != 100:
                raise ValueError(f"Traffic allocation for prompt {prompt_id} must sum to 100%, got {total}%")
        return v

    class Config:
        schema_extra = {
            "example": {
                "name": "Research Assistant Experiment",
                "description": "Testing different versions of the research assistant prompt",
                "is_active": True,
                "start_date": "2023-05-23T00:00:00Z",
                "end_date": "2023-06-23T00:00:00Z",
                "created_by": "<EMAIL>",
                "prompt_versions": {
                    "1": {  # prompt_id
                        "1": 50,  # version_id: traffic_allocation
                        "2": 50
                    }
                }
            }
        }


class ExperimentResponse(BaseModel):
    """Schema for experiment response."""
    id: int
    name: str
    description: Optional[str]
    is_active: bool
    start_date: Optional[datetime]
    end_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    prompt_versions: Dict[int, Dict[int, int]]

    class Config:
        orm_mode = True


# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[str, List[WebSocket]] = {}

    async def connect(self, websocket: WebSocket, prompt_key: str):
        await websocket.accept()
        if prompt_key not in self.active_connections:
            self.active_connections[prompt_key] = []
        self.active_connections[prompt_key].append(websocket)
        logger.info(f"WebSocket client connected for prompt key: {prompt_key}")

    def disconnect(self, websocket: WebSocket, prompt_key: str):
        if prompt_key in self.active_connections:
            if websocket in self.active_connections[prompt_key]:
                self.active_connections[prompt_key].remove(websocket)
            if not self.active_connections[prompt_key]:
                del self.active_connections[prompt_key]
        logger.info(f"WebSocket client disconnected from prompt key: {prompt_key}")

    async def broadcast(self, prompt_key: str, message: Dict[str, Any]):
        if prompt_key in self.active_connections:
            for connection in self.active_connections[prompt_key]:
                await connection.send_json(message)
            logger.info(f"Broadcast message to {len(self.active_connections[prompt_key])} clients for prompt key: {prompt_key}")


# Create connection manager instance
manager = ConnectionManager()


# Endpoints
@router.post("/prompts", response_model=PromptResponse, status_code=201)
async def create_prompt(
    prompt_data: PromptCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new prompt with an initial version.
    """
    # Check if prompt with this key already exists
    result = await db.execute(select(Prompt).where(Prompt.key == prompt_data.key))
    existing_prompt = result.scalars().first()
    
    if existing_prompt:
        raise HTTPException(status_code=409, detail=f"Prompt with key '{prompt_data.key}' already exists")
    
    # Create new prompt
    prompt = Prompt(
        key=prompt_data.key,
        description=prompt_data.description,
        tags=prompt_data.tags,
        is_active=True
    )
    
    db.add(prompt)
    await db.flush()  # Flush to get the prompt ID
    
    # Create initial version
    version = Version(
        prompt_id=prompt.id,
        version=1,  # First version
        content=prompt_data.version.content,
        metadata=prompt_data.version.metadata,
        created_by=prompt_data.version.created_by
    )
    
    db.add(version)
    await db.commit()
    await db.refresh(prompt)
    await db.refresh(version)
    
    # Prepare response
    response = PromptResponse(
        id=prompt.id,
        key=prompt.key,
        description=prompt.description,
        tags=prompt.tags,
        created_at=prompt.created_at,
        updated_at=prompt.updated_at,
        is_active=prompt.is_active,
        latest_version=VersionResponse(
            id=version.id,
            version=version.version,
            content=version.content,
            metadata=version.metadata,
            created_at=version.created_at,
            created_by=version.created_by
        )
    )
    
    return response


@router.get("/prompts/{key}", response_model=PromptResponse)
async def get_prompt(
    key: str = Path(..., description="The prompt key"),
    experiment_id: Optional[int] = Query(None, description="Optional experiment ID for A/B testing"),
    db: AsyncSession = Depends(get_db)
):
    """
    Get a prompt by key. Returns the latest version by default, or a version
    selected by A/B testing if experiment_id is provided.
    """
    # Get prompt by key
    result = await db.execute(select(Prompt).where(Prompt.key == key))
    prompt = result.scalars().first()
    
    if not prompt:
        raise HTTPException(status_code=404, detail=f"Prompt with key '{key}' not found")
    
    if not prompt.is_active:
        raise HTTPException(status_code=410, detail=f"Prompt with key '{key}' is inactive")
    
    # Get the appropriate version
    if experiment_id:
        # Get version from experiment if specified
        result = await db.execute(
            select(Version)
            .join(ExperimentPrompt, Version.id == ExperimentPrompt.version_id)
            .where(
                ExperimentPrompt.experiment_id == experiment_id,
                ExperimentPrompt.prompt_id == prompt.id
            )
            .order_by(func.random())  # Simple random selection for A/B testing
            .limit(1)
        )
        version = result.scalars().first()
        
        if not version:
            # Fall back to latest version if not in experiment
            result = await db.execute(
                select(Version)
                .where(Version.prompt_id == prompt.id)
                .order_by(desc(Version.version))
                .limit(1)
            )
            version = result.scalars().first()
    else:
        # Get latest version
        result = await db.execute(
            select(Version)
            .where(Version.prompt_id == prompt.id)
            .order_by(desc(Version.version))
            .limit(1)
        )
        version = result.scalars().first()
    
    if not version:
        raise HTTPException(status_code=404, detail=f"No versions found for prompt with key '{key}'")
    
    # Prepare response
    response = PromptResponse(
        id=prompt.id,
        key=prompt.key,
        description=prompt.description,
        tags=prompt.tags,
        created_at=prompt.created_at,
        updated_at=prompt.updated_at,
        is_active=prompt.is_active,
        latest_version=VersionResponse(
            id=version.id,
            version=version.version,
            content=version.content,
            metadata=version.metadata,
            created_at=version.created_at,
            created_by=version.created_by
        )
    )
    
    return response


@router.put("/prompts/{id}", response_model=PromptResponse)
async def update_prompt(
    id: int = Path(..., description="The prompt ID"),
    prompt_data: PromptUpdate = Body(...),
    db: AsyncSession = Depends(get_db)
):
    """
    Update a prompt and optionally create a new version.
    """
    # Get prompt by ID
    result = await db.execute(select(Prompt).where(Prompt.id == id))
    prompt = result.scalars().first()
    
    if not prompt:
        raise HTTPException(status_code=404, detail=f"Prompt with ID {id} not found")
    
    # Update prompt fields if provided
    if prompt_data.description is not None:
        prompt.description = prompt_data.description
    
    if prompt_data.tags is not None:
        prompt.tags = prompt_data.tags
    
    if prompt_data.is_active is not None:
        prompt.is_active = prompt_data.is_active
    
    # Create new version if provided
    latest_version = None
    if prompt_data.new_version:
        # Get current latest version number
        result = await db.execute(
            select(func.max(Version.version)).where(Version.prompt_id == prompt.id)
        )
        latest_version_num = result.scalar() or 0
        
        # Create new version
        version = Version(
            prompt_id=prompt.id,
            version=latest_version_num + 1,
            content=prompt_data.new_version.content,
            metadata=prompt_data.new_version.metadata,
            created_by=prompt_data.new_version.created_by
        )
        
        db.add(version)
        await db.flush()
        
        latest_version = version
        
        # Broadcast update to WebSocket clients
        asyncio.create_task(
            manager.broadcast(
                prompt.key,
                {
                    "event": "prompt_updated",
                    "prompt_id": prompt.id,
                    "prompt_key": prompt.key,
                    "version": version.version,
                    "content": version.content
                }
            )
        )
    else:
        # Get latest version for response
        result = await db.execute(
            select(Version)
            .where(Version.prompt_id == prompt.id)
            .order_by(desc(Version.version))
            .limit(1)
        )
        latest_version = result.scalars().first()
    
    await db.commit()
    await db.refresh(prompt)
    
    if latest_version:
        await db.refresh(latest_version)
    
    # Prepare response
    response = PromptResponse(
        id=prompt.id,
        key=prompt.key,
        description=prompt.description,
        tags=prompt.tags,
        created_at=prompt.created_at,
        updated_at=prompt.updated_at,
        is_active=prompt.is_active,
        latest_version=VersionResponse(
            id=latest_version.id,
            version=latest_version.version,
            content=latest_version.content,
            metadata=latest_version.metadata,
            created_at=latest_version.created_at,
            created_by=latest_version.created_by
        ) if latest_version else None
    )
    
    return response


@router.delete("/prompts/{id}", status_code=204)
async def delete_prompt(
    id: int = Path(..., description="The prompt ID"),
    db: AsyncSession = Depends(get_db)
):
    """
    Delete a prompt and all its versions.
    """
    # Get prompt by ID
    result = await db.execute(select(Prompt).where(Prompt.id == id))
    prompt = result.scalars().first()
    
    if not prompt:
        raise HTTPException(status_code=404, detail=f"Prompt with ID {id} not found")
    
    # Delete prompt (cascade will delete versions)
    await db.delete(prompt)
    await db.commit()
    
    return None


@router.post("/experiments", response_model=ExperimentResponse, status_code=201)
async def create_experiment(
    experiment_data: ExperimentCreate,
    db: AsyncSession = Depends(get_db)
):
    """
    Create a new A/B testing experiment.
    """
    # Check if experiment with this name already exists
    result = await db.execute(select(Experiment).where(Experiment.name == experiment_data.name))
    existing_experiment = result.scalars().first()
    
    if existing_experiment:
        raise HTTPException(status_code=409, detail=f"Experiment with name '{experiment_data.name}' already exists")
    
    # Create new experiment
    experiment = Experiment(
        name=experiment_data.name,
        description=experiment_data.description,
        is_active=experiment_data.is_active,
        start_date=experiment_data.start_date,
        end_date=experiment_data.end_date,
        created_by=experiment_data.created_by
    )
    
    db.add(experiment)
    await db.flush()  # Flush to get the experiment ID
    
    # Create experiment-prompt associations
    for prompt_id, versions in experiment_data.prompt_versions.items():
        for version_id, traffic_allocation in versions.items():
            # Verify prompt and version exist
            prompt_result = await db.execute(select(Prompt).where(Prompt.id == prompt_id))
            prompt = prompt_result.scalars().first()
            
            if not prompt:
                await db.rollback()
                raise HTTPException(status_code=404, detail=f"Prompt with ID {prompt_id} not found")
            
            version_result = await db.execute(
                select(Version).where(
                    Version.id == version_id,
                    Version.prompt_id == prompt_id
                )
            )
            version = version_result.scalars().first()
            
            if not version:
                await db.rollback()
                raise HTTPException(
                    status_code=404, 
                    detail=f"Version with ID {version_id} not found for prompt with ID {prompt_id}"
                )
            
            # Create association
            experiment_prompt = ExperimentPrompt(
                experiment_id=experiment.id,
                prompt_id=prompt_id,
                version_id=version_id,
                traffic_allocation=traffic_allocation
            )
            
            db.add(experiment_prompt)
    
    await db.commit()
    await db.refresh(experiment)
    
    # Get prompt versions for response
    result = await db.execute(
        select(ExperimentPrompt).where(ExperimentPrompt.experiment_id == experiment.id)
    )
    experiment_prompts = result.scalars().all()
    
    prompt_versions = {}
    for ep in experiment_prompts:
        if ep.prompt_id not in prompt_versions:
            prompt_versions[ep.prompt_id] = {}
        prompt_versions[ep.prompt_id][ep.version_id] = ep.traffic_allocation
    
    # Prepare response
    response = ExperimentResponse(
        id=experiment.id,
        name=experiment.name,
        description=experiment.description,
        is_active=experiment.is_active,
        start_date=experiment.start_date,
        end_date=experiment.end_date,
        created_at=experiment.created_at,
        updated_at=experiment.updated_at,
        created_by=experiment.created_by,
        prompt_versions=prompt_versions
    )
    
    return response


@router.websocket("/ws/prompts/{key}")
async def websocket_endpoint(websocket: WebSocket, key: str):
    """
    WebSocket endpoint for real-time prompt updates.
    """
    await manager.connect(websocket, key)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket, key)
