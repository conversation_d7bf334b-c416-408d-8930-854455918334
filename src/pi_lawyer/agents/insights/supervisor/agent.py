"""
Supervisor Agent

This module implements the Supervisor Agent, which is responsible for:
1. Classifying user intent in ≤ 1 LLM call
2. Dispatching to the correct node
3. Maintaining tenant isolation
4. Failing gracefully

The Supervisor Agent serves as the entry point for the LangGraph system,
routing requests to the appropriate specialized agent or queuing async jobs.

Usage:
    from pi_lawyer.agents.insights.supervisor.agent import SupervisorAgent

    # Create a supervisor agent
    agent = SupervisorAgent()

    # Execute the agent
    result = await agent.invoke(
        {"messages": [HumanMessage(content="Research statute of limitations")]},
        {"configurable": {"thread_id": "123", "tenant_id": "456"}}
    )
"""

import json
import logging
import os
import hashlib
from typing import Dict, Any, List, Optional, Union, cast
from datetime import datetime, timezone

from langchain_core.messages import HumanMessage, AIMessage, SystemMessage
from langchain_core.runnables import RunnableConfig
from langchain_core.output_parsers.json import JsonOutputParser
from langgraph.types import Command
from pydantic import ValidationError

from pi_lawyer.agents.base_agent import BaseAgent
from pi_lawyer.agents.config import AgentConfig, get_agent_config
from pi_lawyer.shared.core.tools.executor import get_tool_executor
from pi_lawyer.agents.insights.supervisor.schema import Classification, CLASSIFY_FN_SCHEMA
from pi_lawyer.shared.core.tools.enqueue_async_job import enqueue_async_job_tool

# Set up logging
logger = logging.getLogger(__name__)

# Define constants
INTERACTIVE_AGENTS = {
    "intakeAgent",
    "researchAgent",
    "taskCrudAgent",
    "calendarCrudAgent"
}

ASYNC_AGENTS = {
    "documentDraftAgent",
    "insightSwarmAgent"
}

DEFAULT_AGENT = "intakeAgent"
CONFIDENCE_THRESHOLD = float(os.getenv("SUPERVISOR_CONFIDENCE_THRESHOLD", "0.6"))


class SupervisorAgent(BaseAgent):
    """
    Supervisor Agent for AiLex.

    This agent is responsible for:
    1. Classifying user intent in ≤ 1 LLM call
    2. Dispatching to the correct node
    3. Maintaining tenant isolation
    4. Failing gracefully

    It serves as the entry point for the LangGraph system, routing requests
    to the appropriate specialized agent or queuing async jobs.
    """

    def __init__(self, config: Optional[AgentConfig] = None, agent_name: str = "supervisorAgent", node_name: str = ""):
        """
        Initialize the supervisor agent.

        Args:
            config: Agent configuration
            agent_name: Name of the agent for LLM selection
            node_name: Name of the node for LLM selection
        """
        # Use default config if none provided
        if config is None:
            config = get_agent_config("SupervisorAgent")
            if config is None:
                config = AgentConfig(
                    name="supervisorAgent",
                    agent_type="supervisor",
                    description="Supervisor agent that routes requests to specialized agents",
                    version="1.0.0"
                )

        super().__init__(config, agent_name=agent_name, node_name=node_name)

        # Initialize JSON output parser
        self.parser = JsonOutputParser()

        # Initialize simple in-memory cache
        self._cache = {}
        self._cache_max_size = 100
        self._cache_ttl_ms = 500  # milliseconds

        # Load prompt template
        self.prompt_template = self._load_prompt_template()

        logger.info(f"Supervisor agent initialized")

    def _load_prompt_template(self) -> str:
        """
        Load the prompt template from the prompt.md file.

        Returns:
            The prompt template
        """
        import os

        # Get the directory of the current file
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # Construct the path to the prompt.md file
        prompt_path = os.path.join(current_dir, "prompt.md")

        # Read the prompt template
        with open(prompt_path, "r") as f:
            prompt_template = f.read()

        return prompt_template

    def _build_prompt(self, state: Dict[str, Any]) -> str:
        """
        Build the prompt for the LLM.

        Args:
            state: Current state

        Returns:
            The prompt for the LLM
        """
        # Get the last user message
        last_message = None
        for message in reversed(state.get("messages", [])):
            if message.get("type") == "human" or message.get("role") == "user":
                last_message = message
                break

        if last_message is None:
            return "No user message found"

        user_message = last_message.get("content", "")

        # Get tenant and matter information
        tenant_id = state.get("tenant_id", "unknown")
        matter_id = state.get("matter_id", "None")
        locale = state.get("locale", "en-US")

        # Replace placeholders in the prompt template
        prompt = self.prompt_template.replace("{{user_message}}", user_message)
        prompt = prompt.replace("{{tenant_id}}", tenant_id)
        prompt = prompt.replace("{{matter_id or \"None\"}}", matter_id)
        prompt = prompt.replace("{{locale}}", locale)

        return prompt

    async def _classify_intent(self, state: Dict[str, Any]) -> Classification:
        """
        Classify the user's intent using structured output parsing.

        Args:
            state: Current state

        Returns:
            Classification result with agent, args, and confidence
        """
        # Extract the last human message
        last_message = None
        for message in reversed(state.get("messages", [])):
            if message.get("type") == "human" or message.get("role") == "user":
                last_message = message
                break

        if not last_message:
            logger.warning("No human message found in state")
            return Classification(
                agent=DEFAULT_AGENT,
                args={},
                confidence=0.0
            )

        # Get message content
        content = last_message.get("content", "")
        if isinstance(content, list):
            content = " ".join([str(c) for c in content])

        # Check cache for this tenant and message
        tenant_id = state.get("tenant_id", "unknown")
        message_hash = hashlib.md5(f"{tenant_id}:{content}".encode()).hexdigest()

        cache_key = f"{tenant_id}:{message_hash}"
        cached_result = self._cache.get(cache_key)
        if cached_result:
            logger.info(f"Using cached classification for tenant {tenant_id}")
            return cached_result

        # Build the prompt
        prompt = self._build_prompt(state)

        try:
            # Get the LLM client using the selector
            llm = await self._llm(state)

            # Call the LLM with function calling
            response = await llm.chat_completion(
                messages=[{"role": "user", "content": prompt}],
                temperature=0.2,
                max_tokens=500,
                functions=[CLASSIFY_FN_SCHEMA],
                function_call={"name": "classify_intent"}
            )

            # Extract the function call result
            function_call = response.get("choices", [{}])[0].get("message", {}).get("function_call", {})

            if function_call and function_call.get("name") == "classify_intent":
                try:
                    # Parse the arguments
                    args_json = function_call.get("arguments", "{}")
                    result = Classification.parse_raw(args_json)

                    # Cache the result
                    if len(self._cache) >= self._cache_max_size:
                        # Simple LRU: just clear the cache when it gets too big
                        self._cache.clear()
                    self._cache[cache_key] = result

                    return result
                except Exception as e:
                    logger.error(f"Error parsing function call result: {str(e)}")

            # Fallback to content parsing if function call failed
            content = response.get("choices", [{}])[0].get("message", {}).get("content", "")

            try:
                # Try to parse the content as JSON
                parsed_content = json.loads(content)

                # Create a Classification object
                result = Classification(
                    agent=parsed_content.get("agent", DEFAULT_AGENT),
                    args=parsed_content.get("args", {}),
                    confidence=parsed_content.get("confidence", 0.0)
                )

                # Cache the result
                if len(self._cache) >= self._cache_max_size:
                    self._cache.clear()
                self._cache[cache_key] = result

                return result
            except (json.JSONDecodeError, ValidationError):
                logger.warning(f"Failed to parse LLM response: {content}")

            # Fallback to default if all parsing failed
            return Classification(
                agent=DEFAULT_AGENT,
                args={},
                confidence=0.0
            )
        except Exception as e:
            logger.error(f"Error calling LLM for intent classification: {str(e)}")
            return Classification(
                agent=DEFAULT_AGENT,
                args={},
                confidence=0.0
            )

    async def initialize(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Initialize the supervisor agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Initializing supervisor agent")

        # Add a system message if not present
        if "messages" in state and not any(m.get("type") == "system" for m in state["messages"]):
            state["messages"].append({
                "type": "system",
                "content": "I am the AiLex supervisor agent. I'll route your request to the appropriate specialized agent."
            })

        # Initialize memory
        if "memory" in state:
            state["memory"]["initialized_at"] = datetime.now(timezone.utc).isoformat()
            state["memory"]["agent_name"] = self.name
            state["memory"]["agent_type"] = self.agent_type
            state["memory"]["agent_version"] = self.version

        return state

    async def execute(self, state: Dict[str, Any], config: RunnableConfig) -> Union[Dict[str, Any], Command]:
        """
        Execute the supervisor agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state with Command for routing
        """
        logger.info("Executing supervisor agent")

        # Get tenant_id from state
        tenant_id = state.get("tenant_id")
        if not tenant_id:
            logger.warning("No tenant_id found in state, using default")
            tenant_id = "default"

        # Classify the user's intent
        classification = await self._classify_intent(state)

        # Extract agent and args
        agent = classification.agent
        args = classification.args
        confidence = classification.confidence

        logger.info(f"Classified intent: agent={agent}, confidence={confidence}")

        # Check confidence threshold
        confidence_min = float(os.getenv("SUPERVISOR_CONFIDENCE_MIN", str(CONFIDENCE_THRESHOLD)))
        if confidence < confidence_min:
            logger.info(f"Confidence below threshold ({confidence} < {confidence_min}), using default agent")
            agent = DEFAULT_AGENT
            args = {}

        # Store the classification in state
        if "memory" in state:
            state["memory"]["classification"] = classification.dict()

        # Store the args in state for the next agent
        state["agent_args"] = args

        # Handle interactive vs. async agents
        if agent in INTERACTIVE_AGENTS:
            # For interactive agents, return Command to route directly
            logger.info(f"Routing to interactive agent: {agent}")
            return Command(goto=agent)

        elif agent in ASYNC_AGENTS:
            # For async agents, enqueue a job
            logger.info(f"Enqueueing async job for agent: {agent}")

            try:
                # Use the enqueue_async_job_tool directly
                job_result = await enqueue_async_job_tool.execute(
                    tool_name=agent,
                    params=args,
                    tenant_id=tenant_id,
                    user_id=state.get("user_id", "unknown"),
                    thread_id=state.get("thread_id", "unknown"),
                    matter_id=state.get("matter_id")
                )

                # Store the job ID in state
                state["async_job_id"] = job_result.job_id

                # Add a message indicating the job was queued
                if "messages" in state:
                    state["messages"].append({
                        "type": "ai",
                        "content": "I'm working on that... I'll update you soon."
                    })

                logger.info(f"Async job enqueued with ID: {job_result.job_id}")
                return Command(goto="FINISH")

            except Exception as e:
                logger.error(f"Failed to enqueue async job: {str(e)}")

                # Add an error message
                if "messages" in state:
                    state["messages"].append({
                        "type": "ai",
                        "content": "I'm sorry, I encountered an error while processing your request."
                    })

                return Command(goto="FINISH")

        # Default case or unknown agent
        logger.warning(f"Unknown agent type: {agent}, using FINISH")
        return Command(goto="FINISH")

    def _fallback(self, state: Dict[str, Any], reason: str) -> Dict[str, Any]:
        """
        Handle fallback when intent classification fails.

        Args:
            state: Current state
            reason: Reason for fallback

        Returns:
            Updated state
        """
        logger.warning(f"Using fallback due to: {reason}")

        # Set default agent
        state["next"] = DEFAULT_AGENT
        state["agent_args"] = {}

        # Add a message indicating the fallback
        if "messages" in state:
            state["messages"].append({
                "type": "ai",
                "content": "I'm not sure I understood your request. Could you please rephrase it?"
            })

        # Store fallback reason in memory
        if "memory" in state:
            state["memory"]["fallback_reason"] = reason

        return state

    async def cleanup(self, state: Dict[str, Any], config: RunnableConfig) -> Dict[str, Any]:
        """
        Clean up the supervisor agent.

        Args:
            state: Current state
            config: Runnable configuration

        Returns:
            Updated state
        """
        logger.info("Cleaning up supervisor agent")

        # Add cleanup timestamp
        if "memory" in state:
            state["memory"]["cleaned_up_at"] = datetime.now(timezone.utc).isoformat()

        return state
